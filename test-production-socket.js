#!/usr/bin/env node

/**
 * Test script to verify Socket.IO production configuration
 */

const { io } = require('socket.io-client');

const PRODUCTION_API_URL = 'https://api.qwotez.com';
const LOCAL_API_URL = 'http://localhost:3000';

async function testSocketConnection(url, environment) {
  console.log(`\n🧪 Testing ${environment} Socket.IO connection to: ${url}`);

  return new Promise((resolve) => {
    const socket = io(url, {
      withCredentials: true,
      transports: ['polling'],
      timeout: 30000, // Longer timeout
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 1000,
      // Mimic frontend auth
      auth: {
        token: 'test-token',
        userId: 'test-user-id'
      },
    });

    let resolved = false;
    let connected = false;

    socket.on('connect', () => {
      console.log(`✅ ${environment}: Connected successfully!`);
      console.log(`📍 Socket ID: ${socket.id}`);
      connected = true;

      // Test sending a message (like the frontend does)
      console.log(`📤 ${environment}: Testing message send...`);
      socket.emit('test-message', { data: 'test' });

      // Wait longer to see if connection stays stable
      setTimeout(() => {
        if (connected) {
          console.log(`✅ ${environment}: Connection stable after 10 seconds`);
          socket.disconnect();
          if (!resolved) {
            resolved = true;
            resolve({ success: true, error: null });
          }
        }
      }, 10000);
    });

    socket.on('disconnect', (reason) => {
      console.log(`🔌 ${environment}: Disconnected - ${reason}`);
      connected = false;
      if (!resolved) {
        resolved = true;
        resolve({ success: false, error: `Disconnected: ${reason}` });
      }
    });

    socket.on('connect_error', (error) => {
      console.log(`❌ ${environment}: Connection failed`);
      console.log(`🔍 Error: ${error.message}`);
      console.log(`🔍 Error type: ${error.type}`);
      if (!resolved) {
        resolved = true;
        resolve({ success: false, error: error.message });
      }
    });

    // Longer timeout for more realistic testing
    setTimeout(() => {
      if (!resolved) {
        console.log(`⏰ ${environment}: Connection timeout after 15 seconds`);
        socket.disconnect();
        resolved = true;
        resolve({ success: false, error: 'Timeout' });
      }
    }, 15000);
  });
}

async function runTests() {
  console.log('🚀 Testing Socket.IO after CORS fix\n');
  
  const localResult = await testSocketConnection(LOCAL_API_URL, 'Local Development');
  const productionResult = await testSocketConnection(PRODUCTION_API_URL, 'Production');
  
  console.log('\n📊 Test Results:');
  console.log('================');
  console.log(`Local: ${localResult.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Production: ${productionResult.success ? '✅ PASS' : '❌ FAIL'}`);
  
  if (productionResult.success) {
    console.log('\n🎉 SUCCESS! The CORS fix worked!');
  } else {
    console.log(`\n❌ Production error: ${productionResult.error}`);
  }
}

runTests().catch(console.error);
