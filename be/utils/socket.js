const { Server } = require('socket.io')

const io = new Server({
  maxHttpBufferSize: 1e8,
})

const Socket = {
  emit: function (event, data) {
    console.log(event, data)
    io.sockets.emit(event, data)
  },
}

io.use((socket, next) => {
  const token = socket.handshake.auth.token
  const userId = socket.handshake.auth.userId

  console.log('Socket.IO Authentication Middleware:')
  console.log('- Socket ID:', socket.id)
  console.log('- Token present:', !!token)
  console.log('- User ID:', userId)
  console.log('- Origin:', socket.handshake.headers.origin)

  if (token) {
    console.log('✅ Token found - authenticating user')
    auth = true
    room = userId
    userRooms[socket.id] = room

    socket.join(room)
    socket.emit('connected', `⚡ ${socket.id} user just connected to ${room}!`)
    console.log(`✅ User ${userId} joined room ${room}`)

    next()
  } else {
    console.log('⚠️ No token - visitor connection')
    auth = false

    socket.emit('connected', '⚡ Visitor Connected')

    next()
  }
})

let room = ''
let userRooms = ''
let auth = false

const onlineUsers = new Map()

io.on('connection', socket => {
  console.log(`🔌 Socket.IO connection established: ${socket.id}`)
  console.log(`🔐 Auth status: ${auth}`)
  console.log(`📍 Client origin: ${socket.handshake.headers.origin}`)
  console.log(`🌐 Client IP: ${socket.handshake.address}`)
  console.log(`🚀 Transport: ${socket.conn.transport.name}`)
  console.log(`🔗 User Agent: ${socket.handshake.headers['user-agent']?.substring(0, 100)}`)

  // Enhanced error handling for the socket
  socket.on('error', error => {
    console.error(`❌ Socket error for ${socket.id}:`, error)
    console.log('🔧 Implementing server-side error recovery...')

    // Try to maintain connection on error
    if (socket.connected) {
      socket.emit('server-error-recovery', {
        message: 'Server detected error but connection maintained',
        timestamp: new Date().toISOString(),
      })
    }
  })

  socket.on('disconnect', reason => {
    console.log(`🔌 Socket ${socket.id} disconnected: ${reason}`)
    console.log(`🕐 Connection duration: ${Date.now() - socket.handshake.time}ms`)

    // Log disconnect reasons for debugging
    if (reason === 'transport error' || reason === 'transport close') {
      console.log('🌐 Network-related disconnect detected')
    } else if (reason === 'ping timeout') {
      console.log('⏰ Ping timeout disconnect - possible network instability')
    }
  })

  // Monitor connection health
  socket.on('ping', () => {
    console.log(`💓 Ping from ${socket.id}`)
  })

  socket.on('pong', latency => {
    console.log(`💓 Pong from ${socket.id} - latency: ${latency}ms`)
  })

  if (auth) {
    // Import Models
    const AppNotification = require('../models/app-notification/app-notification')

    // Import Controllers
    const {
      getGlobalNotifications,
      sendGlobalNotification,
      sendSingleUserNotification,
      markAppAsRead,
      markAppAsUnread,
    } = require('../controllers/app-notification/app-notification.controller')

    const {
      forgotPassword,
      resetPassword,
      getMe,
      updateDetails,
      updatePassword,
      resendVerifyToken,
      setup2fa,
      verify2fa,
      reset2fa,
      disableOtp,
      verifyOTP,
      sendOTP,
      logout,
    } = require('../controllers/auth.controller')

    const {
      getUsers,
      getUser,
      createUser,
      updateUser,
      deleteUser,
      updateUserMaxIdentities,
      removeMaxIdentitiesOverride,
      getUserIdentityUsage,
      bulkUpdateMaxIdentities,
      getIdentityUsageAnalytics,
      getUsersApproachingLimits,
      bulkRemoveMaxIdentitiesOverride,
    } = require('../controllers/user/user.controller')

    const {
      getUserSubscriptionDetails,
      updateUserSubscription,
    } = require('../controllers/user/user-subscription.controller')

    const {
      getUserThemeSettings,
      updateUserThemeSettings,
      resetUserThemeSettings,
    } = require('../controllers/user/theme-setting.controller')

    const {
      getProfiles,
      getProfile,
      createProfile,
      updateProfile,
      deleteProfile,
      uploadCoverImage,
      uploadProfileImage,
      activity,
    } = require('../controllers/profile.controller')

    const {
      getNotifications,
      getNotification,
      createNotification,
      updateNotification,
      deleteNotification,
      markAllAsRead,
      markAllAsUnread,
      markAsRead,
      markAsUnread,
    } = require('../controllers/notification/notification.controller')

    const {
      getNotificationSettings,
      createNotificationSetting,
      updateNotificationSetting,
    } = require('../controllers/user/notification-setting.controller')

    const { createReferralLink } = require('../controllers/referral/referral-link.controller')
    const { createReferralFee } = require('../controllers/referral/referral-fee.controller')
    const { createReferralDiscount } = require('../controllers/referral/referral-discount.controller')

    const {
      getAllReferrals,
      getReferralStats,
      createReferralInvite,
      getReferralLink,
      updateReferralStatus,
      deleteReferral,
    } = require('../controllers/referral/referral.controller')

    const {
      getIdentities,
      getIdentity,
      createFromOnboarding,
      createIdentity,
      updateIdentity,
      deleteIdentity,
    } = require('../controllers/identity/identity.controller')

    const {
      getBankAccounts,
      getBankAccount,
      createBankAccount,
      updateBankAccount,
      deleteBankAccount,
    } = require('../controllers/business-management/bank-account.controller')

    const {
      getCurrencies,
      getCurrency,
      createCurrency,
      updateCurrency,
      deleteCurrency,
    } = require('../controllers/business-management/currency.controller')

    const {
      getAgentRates,
      getAgentRate,
      createAgentRate,
      updateAgentRate,
      deleteAgentRate,
    } = require('../controllers/business-management/agent-rate.controller')

    const {
      getCrewRates,
      getCrewRate,
      createCrewRate,
      updateCrewRate,
      deleteCrewRate,
    } = require('../controllers/business-management/crew-rate.controller')

    const {
      getStaffRates,
      getStaffRate,
      createStaffRate,
      updateStaffRate,
      deleteStaffRate,
    } = require('../controllers/business-management/staff-rate.controller')

    const {
      getClients,
      getClient,
      createClient,
      updateClient,
      deleteClient,
    } = require('../controllers/client/client.controller')

    const {
      getContractTemplates,
      getContractTemplate,
      createContractTemplate,
      updateContractTemplate,
      deleteContractTemplate,
    } = require('../controllers/contract/contract-template.controller')

    const {
      getContracts,
      getContract,
      createContract,
      updateContract,
      deleteContract,
    } = require('../controllers/contract/contract.controller')

    const {
      getClientInvites,
      getClientInvite,
      createClientInvite,
      updateClientInviteStatus,
      deleteClientInvite,
      checkIfInviteExpired,
    } = require('../controllers/client/client-invite.controller')

    const {
      getAllRateCards,
      getRateCard,
      createRateCard,
      updateRateCard,
      deleteRateCard,
    } = require('../controllers/rate-card/rate-card.controller')

    const {
      getAllRateCardTemplates,
      getRateCardTemplate,
      createRateCardTemplate,
      updateRateCardTemplate,
      deleteRateCardTemplate,
    } = require('../controllers/rate-card/template.controller')

    const {
      getPortfolios,
      getPortfolio,
      getPortfolioBySlug,
      createPortfolio,
      updatePortfolio,
      deletePortfolio,
      getPortfolioImages,
    } = require('../controllers/portfolio/portfolio.controller')

    const {
      uploadPortfolioImages,
      deletePortfolioImage,
      getUploadedPortfolioImages,
    } = require('../controllers/portfolio/portfolio-images.controller')

    const {
      getExpenses,
      getExpense,
      createExpense,
      updateExpense,
      deleteExpense,
    } = require('../controllers/expense/expense.controller')

    const {
      getExpenseCategories,
      getExpenseCategory,
      createExpenseCategory,
      updateExpenseCategory,
      deleteExpenseCategory,
    } = require('../controllers/expense/expense-category.controller')

    const {
      getRentals,
      getRental,
      createRental,
      updateRental,
      deleteRental,
    } = require('../controllers/rental/rental.controller')

    const {
      getCategories: getRentalCategories,
      getCategory: getRentalCategory,
      createCategory: createRentalCategory,
      updateCategory: updateRentalCategory,
      deleteCategory: deleteRentalCategory,
    } = require('../controllers/rental/rental-category.controller')

    const {
      getBookings: getRentalBookings,
      getBooking: getRentalBooking,
      createBooking: createRentalBooking,
      updateBooking: updateRentalBooking,
      deleteBooking: deleteRentalBooking,
      changeBookingStatus: changeRentalBookingStatus,
      checkOutBooking,
      checkInBooking,
    } = require('../controllers/rental/rental-booking.controller')

    const {
      getTickets,
      getTicket,
      createTicket,
      updateTicket,
      deleteTicket,
      addComment,
      assignTicket,
    } = require('../controllers/support/support-ticket.controller')

    const {
      getWeeklyInvoiceTotals,
      earningsWidgets,
      earningsReports,
      earningsPastYears,
      jobStatuses,
      getWeeklyMetrics,
      getMonthlyMetrics,
      getProfitLossReport,
      getTotalSales,
      getWeeklySalesMetrics,
      getWeeklyProfitLossReport,
      getMonthlyProfitLossReport,
      getYearlyProfitLossReport,
    } = require('../controllers/user/dashboard.controller')

    const {
      getGroups,
      getGroup,
      createGroup,
      updateGroup,
      deleteGroup,
    } = require('../controllers/line-item-group/group.controller')

    const {
      getCategories,
      getCategory,
      createCategory,
      updateCategory,
      deleteCategory,
    } = require('../controllers/line-item-group/category.controller')

    const {
      getSubCategories,
      getSubCategory,
      createSubCategory,
      updateSubCategory,
      deleteSubCategory,
    } = require('../controllers/line-item-group/sub-category.controller')

    const { getJobs, getJob, createJob, updateJob, deleteJob } = require('../controllers/job/job.controller')

    const {
      getEstimates,
      getEstimate,
      createEstimate,
      updateEstimate,
      deleteEstimate,
    } = require('../controllers/production/estimate.controller')

    const {
      getProductions,
      getProduction,
      createProduction,
      updateProduction,
      deleteProduction,
    } = require('../controllers/production/production.controller')

    const {
      getProductionInvoices,
      getProductionInvoice,
      createProductionInvoice,
      updateProductionInvoice,
      deleteProductionInvoice,
    } = require('../controllers/production/invoice.controller')

    const {
      getQuotes,
      getQuote,
      getFrontendQuote,
      createQuote,
      updateQuote,
      deleteQuote,
      sendQuote,
    } = require('../controllers/quote-invoice/quote.controller')

    const {
      getInvoices,
      getInvoice,
      createInvoice,
      updateInvoice,
      deleteInvoice,
      sendInvoice,
    } = require('../controllers/quote-invoice/invoice.controller')

    const {
      getAllBookings,
      getBookingsByDateRange,
      getBooking,
      createBooking,
      updateBooking,
      deleteBooking,
    } = require('../controllers/booking/booking.controller')

    const {
      getAllTemplates,
      getTemplate,
      createTemplate,
      updateTemplate,
      deleteTemplate,
    } = require('../controllers/booking/booking-template-controller')

    const {
      getAllInvites,
      getInvite,
      getInviteByToken,
      createInvite,
      updateInvite,
      deleteInvite,
      acceptInvite,
    } = require('../controllers/collaborator/collaborator-invite.controller')

    const {
      getAllCollaborators,
      getCollaborator,
      createCollaborator,
      updateCollaborator,
      deleteCollaborator,
    } = require('../controllers/collaborator/collaborator.controller')

    const {
      getAllEvents,
      getEventsByDateRange,
      getEvent,
      createEvent,
      updateEvent,
      deleteEvent,
    } = require('../controllers/event/event.controller')

    const {
      getAllCalendars,
      getAllFilteredCalendars,
      getCalendar,
      createCalendar,
      updateCalendar,
      deleteCalendar,
    } = require('../controllers/tools/calendar.controller')

    const {
      getAllMoodBoards,
      getMoodBoard,
      createMoodBoard,
      updateMoodBoard,
      deleteMoodBoard,
      duplicateMoodBoard,
      addMoodBoardItem,
      updateMoodBoardItem,
      deleteMoodBoardItem,
    } = require('../controllers/tools/mood-board.controller')

    const {
      getAllKanbans,
      getKanban,
      createKanban,
      updateKanban,
      deleteKanban,
      deleteKanbanBoard,
      addItemToKanban,
      removeItemFromKanban,
      updateItemState,
      updateBoardState,
      addBoard,
      renameBoard,
      updateItem,
    } = require('../controllers/tools/kanban.controller')

    const {
      getAllTodoChecklists,
      getTodoChecklist,
      createTodoChecklist,
      updateTodoChecklist,
      deleteTodoChecklist,
      addTodoItem,
      updateTodoItem,
      deleteTodoItem,
      toggleTodoItem,
      reorderTodoItems,
    } = require('../controllers/tools/todo-checklist.controller')

    const {
      getAllQuestionnaires,
      getQuestionnaire,
      createQuestionnaire,
      updateQuestionnaire,
      deleteQuestionnaire,
    } = require('../controllers/questionnaire/questionnaire.controller')

    const {
      getQuestionQuestionnaire,
      createQuestionnaireQuestion,
      updateQuestionnaireQuestion,
      deleteQuestionnaireQuestion,
    } = require('../controllers/questionnaire/questionnaire-question.controller')

    const {
      getAnswersByQuestionnaire,
      submitAnswers,
    } = require('../controllers/questionnaire/questionnaire-answer.controller')

    const {
      getAllOnboardingTemplates,
      getOnboardingTemplate,
      createOnboardingTemplate,
      updateOnboardingTemplate,
      deleteOnboardingTemplate,
    } = require('../controllers/onboarding/onboarding-template.controller')

    const {
      getAllPortfolioTemplates,
      getPortfolioTemplate,
      createPortfolioTemplate,
      updatePortfolioTemplate,
      deletePortfolioTemplate,
      clonePortfolioTemplate,
    } = require('../controllers/portfolio/portfolio-template.controller')

    const {
      getAllTermConditionTemplates,
      getTermConditionTemplate,
      createTermConditionTemplate,
      updateTermConditionTemplate,
      deleteTermConditionTemplate,
    } = require('../controllers/term-condition/template.controller')

    const {
      getAllTermConditions,
      getTermCondition,
      createTermCondition,
      updateTermCondition,
      deleteTermCondition,
    } = require('../controllers/term-condition/term-condition.controller')

    const {
      getTeams,
      getTeam,
      createTeam,
      updateTeam,
      deleteTeam,
      addMember,
      removeMember,
      updateMemberRole,
    } = require('../controllers/team/team.controller')

    const {
      getTeamInvites,
      getTeamInvite,
      createTeamInvite,
      acceptTeamInvite,
      rejectTeamInvite,
      cancelTeamInvite,
      getTeamInviteByToken,
      resendTeamInvite,
      cleanupExpiredInvites,
    } = require('../controllers/team/team-invite.controller')

    const {
      getChats,
      getChat,
      createIndividualChat,
      createGroupChat,
      updateChat,
      leaveChat,
      addParticipants,
      getAllChatsAdmin,
      getChatAnalytics,
    } = require('../controllers/chat/chat.controller')

    const {
      sendMessage,
      getMessages,
      editMessage,
      deleteMessage,
      addReaction,
      removeReaction,
      markMessagesAsRead,
      adminDeleteMessage,
      getMessageAnalytics,
    } = require('../controllers/chat/message.controller')

    const {
      inviteToGroup,
      acceptGroupInvite,
      rejectGroupInvite,
      getGroupInvites,
      cancelGroupInvite,
    } = require('../controllers/chat/group.controller')

    // Online Users
    socket.on('user-online', async userId => {
      onlineUsers.set(userId, socket.id)
      console.log(`User ${ userId } is online`)

      // ✅ Query unread notifications
      try {
        const notifications = await AppNotification.find({
          user: userId,
          isRead: false,
        })

        // ✅ Emit unread notifications to this user's socket
        if (notifications.length > 0) {
          socket.emit('notifications', notifications)
          console.log(`Sent ${ notifications.length } notifications to user ${ userId }`)
        }
      } catch (err) {
        console.error('Error fetching notifications:', err)
      }
    })

    socket.on('disconnect', () => {
      for (const [userId, sockId] of onlineUsers.entries()) {
        if (sockId === socket.id) {
          onlineUsers.delete(userId)
          console.log(`User ${ userId } went offline`)
          break
        }
      }
    })

    // App Notifications
    socket.on('appNotification:getGlobalNotifications', getGlobalNotifications)
    socket.on('appNotification:sendGlobalNotification', sendGlobalNotification)
    socket.on('appNotification:sendSingleUserNotification', sendSingleUserNotification)
    socket.on('appNotification:markAsRead', markAppAsRead)
    socket.on('appNotification:markAsUnread', markAppAsUnread)

    // Auth
    socket.on('authController:forgotPassword', forgotPassword)
    socket.on('authController:resetPassword', resetPassword)
    socket.on('authController:getMe', getMe)
    socket.on('authController:updateDetails', updateDetails)
    socket.on('authController:updatePassword', updatePassword)
    socket.on('authController:resendVerifyAccount', resendVerifyToken)
    socket.on('authController:setup2fa', setup2fa)
    socket.on('authController:verify2fa', verify2fa)
    socket.on('authController:reset2fa', reset2fa)
    socket.on('authController:disableOtp', disableOtp)
    socket.on('authController:verifyOTP', verifyOTP)
    socket.on('authController:sendOTP', sendOTP)
    socket.on('authController:logout', logout)

    // Subscriptions
    socket.on('userSubscriptionController:updateUserSubscription', updateUserSubscription)
    socket.on('userSubscriptionController:getUserSubscriptionDetails', getUserSubscriptionDetails)

    // Theme Settings
    socket.on('themeSettingController:getUserThemeSettings', getUserThemeSettings)
    socket.on('themeSettingController:updateUserThemeSettings', updateUserThemeSettings)
    socket.on('themeSettingController:resetUserThemeSettings', resetUserThemeSettings)

    // Referral
    socket.on('referralController:createReferralLink', createReferralLink)
    socket.on('referralController:createReferralFee', createReferralFee)
    socket.on('referralController:createReferralDiscount', createReferralDiscount)
    socket.on('referralController:getAllReferrals', getAllReferrals)
    socket.on('referralController:getReferralStats', getReferralStats)
    socket.on('referralController:createReferralInvite', createReferralInvite)
    socket.on('referralController:getReferralLink', getReferralLink)
    socket.on('referralController:updateReferralStatus', updateReferralStatus)
    socket.on('referralController:deleteReferral', deleteReferral)

    // Users
    socket.on('userController:getUsers', getUsers)
    socket.on('userController:getUser', getUser)
    socket.on('userController:createUser', createUser)
    socket.on('userController:updateUser', updateUser)
    socket.on('userController:deleteUser', deleteUser)
    socket.on('userController:updateUserMaxIdentities', updateUserMaxIdentities)
    socket.on('userController:removeMaxIdentitiesOverride', removeMaxIdentitiesOverride)
    socket.on('userController:getUserIdentityUsage', getUserIdentityUsage)
    socket.on('userController:bulkUpdateMaxIdentities', bulkUpdateMaxIdentities)
    socket.on('userController:getIdentityUsageAnalytics', getIdentityUsageAnalytics)
    socket.on('userController:getUsersApproachingLimits', getUsersApproachingLimits)
    socket.on('userController:bulkRemoveMaxIdentitiesOverride', bulkRemoveMaxIdentitiesOverride)

    // Profiles
    socket.on('profileController:getProfiles', getProfiles)
    socket.on('profileController:getProfile', getProfile)
    socket.on('profileController:createProfile', createProfile)
    socket.on('profileController:updateProfile', updateProfile)
    socket.on('profileController:deleteProfile', deleteProfile)
    socket.on('profileController:uploadProfileImage', uploadProfileImage)
    socket.on('profileController:uploadCoverImage', uploadCoverImage)
    socket.on('profileController:activity', activity)

    // Notifications
    socket.on('notificationController:getNotifications', getNotifications)
    socket.on('notificationController:getNotification', getNotification)
    socket.on('notificationController:createNotification', createNotification)
    socket.on('notificationController:updateNotification', updateNotification)
    socket.on('notificationController:deleteNotification', deleteNotification)
    socket.on('notificationController:markAllAsRead', markAllAsRead)
    socket.on('notificationController:markAllAsUnread', markAllAsUnread)
    socket.on('notificationController:markAsReadNotification', markAsRead)
    socket.on('notificationController:markAsUnreadNotification', markAsUnread)

    // Notification Settings
    socket.on('notificationSettingController:getNotificationSettings', getNotificationSettings)
    socket.on('notificationSettingController:createNotificationSetting', createNotificationSetting)
    socket.on('notificationSettingController:updateNotificationSetting', updateNotificationSetting)

    // Identities
    socket.on('identityController:getIdentities', getIdentities)
    socket.on('identityController:getIdentity', getIdentity)
    socket.on('identityController:createOnboardingIdentity', createFromOnboarding)
    socket.on('identityController:createIdentity', createIdentity)
    socket.on('identityController:updateIdentity', updateIdentity)
    socket.on('identityController:deleteIdentity', deleteIdentity)

    // Bank Accounts
    socket.on('bankAccountController:getBankAccounts', getBankAccounts)
    socket.on('bankAccountController:getBankAccount', getBankAccount)
    socket.on('bankAccountController:createBankAccount', createBankAccount)
    socket.on('bankAccountController:updateBankAccount', updateBankAccount)
    socket.on('bankAccountController:deleteBankAccount', deleteBankAccount)

    // Currencies
    socket.on('currencyController:getCurrencies', getCurrencies)
    socket.on('currencyController:getCurrency', getCurrency)
    socket.on('currencyController:createCurrency', createCurrency)
    socket.on('currencyController:updateCurrency', updateCurrency)
    socket.on('currencyController:deleteCurrency', deleteCurrency)

    // Agent Rates
    socket.on('agentRateController:getAgentRates', getAgentRates)
    socket.on('agentRateController:getAgentRate', getAgentRate)
    socket.on('agentRateController:createAgentRate', createAgentRate)
    socket.on('agentRateController:updateAgentRate', updateAgentRate)
    socket.on('agentRateController:deleteAgentRate', deleteAgentRate)

    // Crew Rates
    socket.on('crewRateController:getCrewRates', getCrewRates)
    socket.on('crewRateController:getCrewRate', getCrewRate)
    socket.on('crewRateController:createCrewRate', createCrewRate)
    socket.on('crewRateController:updateCrewRate', updateCrewRate)
    socket.on('crewRateController:deleteCrewRate', deleteCrewRate)

    // Staff Rates
    socket.on('staffRateController:getStaffRates', getStaffRates)
    socket.on('staffRateController:getStaffRate', getStaffRate)
    socket.on('staffRateController:createStaffRate', createStaffRate)
    socket.on('staffRateController:updateStaffRate', updateStaffRate)
    socket.on('staffRateController:deleteStaffRate', deleteStaffRate)

    // Rate Cards - Enhanced with proper request structure
    socket.on('rateCardController:getRateCards', getAllRateCards)
    socket.on('rateCardController:getRateCard', getRateCard)
    socket.on('rateCardController:createRateCard', createRateCard)
    socket.on('rateCardController:updateRateCard', updateRateCard)
    socket.on('rateCardController:deleteRateCard', deleteRateCard)

    // Rate Card Templates - Enhanced with proper request structure
    socket.on('rateCardTemplateController:getRateCardTemplates', getAllRateCardTemplates)
    socket.on('rateCardTemplateController:getRateCardTemplate', getRateCardTemplate)
    socket.on('rateCardTemplateController:createRateCardTemplate', createRateCardTemplate)
    socket.on('rateCardTemplateController:updateRateCardTemplate', updateRateCardTemplate)
    socket.on('rateCardTemplateController:deleteRateCardTemplate', deleteRateCardTemplate)

    // Clients
    socket.on('clientController:getClients', getClients)
    socket.on('clientController:getClient', getClient)
    socket.on('clientController:createClient', createClient)
    socket.on('clientController:updateClient', updateClient)
    socket.on('clientController:deleteClient', deleteClient)

    // Client Invites
    socket.on('clientInviteController:getClientInvites', getClientInvites)
    socket.on('clientInviteController:getClientInvite', getClientInvite)
    socket.on('clientInviteController:createClientInvite', createClientInvite)
    socket.on('clientInviteController:updateClientInviteStatus', updateClientInviteStatus)
    socket.on('clientInviteController:deleteClientInvite', deleteClientInvite)
    socket.on('clientInviteController:checkIfInviteExpired', checkIfInviteExpired)

    // Contracts
    socket.on('contractTemplateController:getContractTemplates', getContractTemplates)
    socket.on('contractTemplateController:getContractTemplate', getContractTemplate)
    socket.on('contractTemplateController:createContractTemplate', createContractTemplate)
    socket.on('contractTemplateController:updateContractTemplate', updateContractTemplate)
    socket.on('contractTemplateController:deleteContractTemplate', deleteContractTemplate)

    socket.on('contractController:getContracts', getContracts)
    socket.on('contractController:getContract', getContract)
    socket.on('contractController:createContract', createContract)
    socket.on('contractController:updateContract', updateContract)
    socket.on('contractController:deleteContract', deleteContract)

    // Dashboards
    socket.on('dashboardController:getWeeklyInvoiceTotals', getWeeklyInvoiceTotals)
    socket.on('dashboardController:earningsWidgets', earningsWidgets)
    socket.on('dashboardController:earningsReports', earningsReports)
    socket.on('dashboardController:earningsPastYears', earningsPastYears)
    socket.on('dashboardController:jobStatuses', jobStatuses)
    socket.on('dashboardController:getWeeklyMetrics', getWeeklyMetrics)
    socket.on('dashboardController:getMonthlyMetrics', getMonthlyMetrics)
    socket.on('dashboardController:getProfitLossReport', getProfitLossReport)
    socket.on('dashboardController:getTotalSales', getTotalSales)
    socket.on('dashboardController:getWeeklySalesMetrics', getWeeklySalesMetrics)
    socket.on('dashboardController:getWeeklyProfitLossReport', getWeeklyProfitLossReport)
    socket.on('dashboardController:getMonthlyProfitLossReport', getMonthlyProfitLossReport)
    socket.on('dashboardController:getYearlyProfitLossReport', getYearlyProfitLossReport)

    // Groups
    socket.on('groupController:getGroups', getGroups)
    socket.on('groupController:getGroup', getGroup)
    socket.on('groupController:createGroup', createGroup)
    socket.on('groupController:updateGroup', updateGroup)
    socket.on('groupController:deleteGroup', deleteGroup)

    // Categories
    socket.on('categoryController:getCategories', getCategories)
    socket.on('categoryController:getCategory', getCategory)
    socket.on('categoryController:createCategory', createCategory)
    socket.on('categoryController:updateCategory', updateCategory)
    socket.on('categoryController:deleteCategory', deleteCategory)

    // Sub Categories
    socket.on('subCategoryController:getSubCategories', getSubCategories)
    socket.on('subCategoryController:getSubCategory', getSubCategory)
    socket.on('subCategoryController:createSubCategory', createSubCategory)
    socket.on('subCategoryController:updateSubCategory', updateSubCategory)
    socket.on('subCategoryController:deleteSubCategory', deleteSubCategory)

    // Jobs
    socket.on('jobController:getJobs', getJobs)
    socket.on('jobController:getJob', getJob)
    socket.on('jobController:createJob', createJob)
    socket.on('jobController:updateJob', updateJob)
    socket.on('jobController:deleteJob', deleteJob)

    // Estimates
    socket.on('estimateController:getEstimates', getEstimates)
    socket.on('estimateController:getEstimate', getEstimate)
    socket.on('estimateController:createEstimate', createEstimate)
    socket.on('estimateController:updateEstimate', updateEstimate)
    socket.on('estimateController:deleteEstimate', deleteEstimate)

    // Productions
    socket.on('productionController:getProductions', getProductions)
    socket.on('productionController:getProduction', getProduction)
    socket.on('productionController:createProduction', createProduction)
    socket.on('productionController:updateProduction', updateProduction)
    socket.on('productionController:deleteProduction', deleteProduction)

    // Production Invoices
    socket.on('productionInvoiceController:getProductionInvoices', getProductionInvoices)
    socket.on('productionInvoiceController:getProductionInvoice', getProductionInvoice)
    socket.on('productionInvoiceController:createProductionInvoice', createProductionInvoice)
    socket.on('productionInvoiceController:updateProductionInvoice', updateProductionInvoice)
    socket.on('productionInvoiceController:deleteProductionInvoice', deleteProductionInvoice)

    // Quotes
    socket.on('quoteController:getQuotes', getQuotes)
    socket.on('quoteController:getQuote', getQuote)
    socket.on('quoteController:getFrontendQuote', getFrontendQuote)
    socket.on('quoteController:createQuote', createQuote)
    socket.on('quoteController:updateQuote', updateQuote)
    socket.on('quoteController:deleteQuote', deleteQuote)
    socket.on('quoteController:sendQuote', sendQuote)

    // Invoices
    socket.on('invoiceController:getInvoices', getInvoices)
    socket.on('invoiceController:getInvoice', getInvoice)
    socket.on('invoiceController:createInvoice', createInvoice)
    socket.on('invoiceController:updateInvoice', updateInvoice)
    socket.on('invoiceController:deleteInvoice', deleteInvoice)
    socket.on('invoiceController:sendInvoice', sendInvoice)

    // Bookings
    socket.on('bookingController:getAllBookings', getAllBookings)
    socket.on('bookingController:getBookingsByDateRange', getBookingsByDateRange)
    socket.on('bookingController:getBooking', getBooking)
    socket.on('bookingController:createBooking', createBooking)
    socket.on('bookingController:updateBooking', updateBooking)
    socket.on('bookingController:deleteBooking', deleteBooking)

    // Booking Templates
    socket.on('bookingTemplateController:getAllTemplates', getAllTemplates)
    socket.on('bookingTemplateController:getTemplate', getTemplate)
    socket.on('bookingTemplateController:createTemplate', createTemplate)
    socket.on('bookingTemplateController:updateTemplate', updateTemplate)
    socket.on('bookingTemplateController:deleteTemplate', deleteTemplate)

    // Collaborator Invites
    socket.on('collaboratorInviteController:getAllInvites', getAllInvites)
    socket.on('collaboratorInviteController:getInvite', getInvite)
    socket.on('collaboratorInviteController:getInviteByToken', getInviteByToken)
    socket.on('collaboratorInviteController:createInvite', createInvite)
    socket.on('collaboratorInviteController:updateInvite', updateInvite)
    socket.on('collaboratorInviteController:deleteInvite', deleteInvite)
    socket.on('collaboratorInviteController:acceptInvite', acceptInvite)

    // Collaborators
    socket.on('collaboratorController:getAllCollaborators', getAllCollaborators)
    socket.on('collaboratorController:getCollaborator', getCollaborator)
    socket.on('collaboratorController:createCollaborator', createCollaborator)
    socket.on('collaboratorController:updateCollaborator', updateCollaborator)
    socket.on('collaboratorController:deleteCollaborator', deleteCollaborator)

    // Events
    socket.on('eventController:getAllEvents', getAllEvents)
    socket.on('eventController:getEventsByDateRange', getEventsByDateRange)
    socket.on('eventController:getEvent', getEvent)
    socket.on('eventController:createEvent', createEvent)
    socket.on('eventController:updateEvent', updateEvent)
    socket.on('eventController:deleteEvent', deleteEvent)

    // Calendars
    socket.on('calendarController:getAllCalendars', getAllCalendars)
    socket.on('calendarController:getAllFilteredCalendars', getAllFilteredCalendars)
    socket.on('calendarController:getCalendar', getCalendar)
    socket.on('calendarController:createCalendar', createCalendar)
    socket.on('calendarController:updateCalendar', updateCalendar)
    socket.on('calendarController:deleteCalendar', deleteCalendar)

    // Mood Boards
    socket.on('moodBoardController:getAllMoodBoards', getAllMoodBoards)
    socket.on('moodBoardController:getMoodBoard', getMoodBoard)
    socket.on('moodBoardController:createMoodBoard', createMoodBoard)
    socket.on('moodBoardController:updateMoodBoard', updateMoodBoard)
    socket.on('moodBoardController:deleteMoodBoard', deleteMoodBoard)
    socket.on('moodBoardController:duplicateMoodBoard', duplicateMoodBoard)
    socket.on('moodBoardController:addMoodBoardItem', addMoodBoardItem)
    socket.on('moodBoardController:updateMoodBoardItem', updateMoodBoardItem)
    socket.on('moodBoardController:deleteMoodBoardItem', deleteMoodBoardItem)

    // Kanbans
    socket.on('kanbanController:getAllKanbans', getAllKanbans)
    socket.on('kanbanController:getKanban', getKanban)
    socket.on('kanbanController:createKanban', createKanban)
    socket.on('kanbanController:updateKanban', updateKanban)
    socket.on('kanbanController:deleteKanban', deleteKanban)
    socket.on('kanbanController:deleteKanbanBoard', deleteKanbanBoard)
    socket.on('kanbanController:addItemToKanban', addItemToKanban)
    socket.on('kanbanController:removeItemFromKanban', removeItemFromKanban)
    socket.on('kanbanController:updateItemState', updateItemState)
    socket.on('kanbanController:updateBoardState', updateBoardState)
    socket.on('kanbanController:addBoard', addBoard)
    socket.on('kanbanController:renameBoard', renameBoard)
    socket.on('kanbanController:updateItem', updateItem)

    // Todo Checklists
    socket.on('todoChecklistController:getAllTodoChecklists', getAllTodoChecklists)
    socket.on('todoChecklistController:getTodoChecklist', getTodoChecklist)
    socket.on('todoChecklistController:createTodoChecklist', createTodoChecklist)
    socket.on('todoChecklistController:updateTodoChecklist', updateTodoChecklist)
    socket.on('todoChecklistController:deleteTodoChecklist', deleteTodoChecklist)
    socket.on('todoChecklistController:addTodoItem', addTodoItem)
    socket.on('todoChecklistController:updateTodoItem', updateTodoItem)
    socket.on('todoChecklistController:deleteTodoItem', deleteTodoItem)
    socket.on('todoChecklistController:toggleTodoItem', toggleTodoItem)
    socket.on('todoChecklistController:reorderTodoItems', reorderTodoItems)

    // Questionnaires
    socket.on('questionnaireController:getAllQuestionnaires', getAllQuestionnaires)
    socket.on('questionnaireController:getQuestionnaire', getQuestionnaire)
    socket.on('questionnaireController:createQuestionnaire', createQuestionnaire)
    socket.on('questionnaireController:updateQuestionnaire', updateQuestionnaire)
    socket.on('questionnaireController:deleteQuestionnaire', deleteQuestionnaire)

    // Questionnaire Questions
    socket.on('questionnaireQuestionController:getQuestionQuestionnaire', getQuestionQuestionnaire)
    socket.on('questionnaireQuestionController:createQuestionnaireQuestion', createQuestionnaireQuestion)
    socket.on('questionnaireQuestionController:updateQuestionnaireQuestion', updateQuestionnaireQuestion)
    socket.on('questionnaireQuestionController:deleteQuestionnaireQuestion', deleteQuestionnaireQuestion)

    // Questionnaire Answers
    socket.on('questionnaireAnswerController:getAnswersByQuestionnaire', getAnswersByQuestionnaire)
    socket.on('questionnaireAnswerController:submitAnswers', submitAnswers)

    // Portfolios
    socket.on('portfolioController:getPortfolios', getPortfolios)
    socket.on('portfolioController:getPortfolio', getPortfolio)
    socket.on('portfolioController:getPortfolioBySlug', getPortfolioBySlug)
    socket.on('portfolioController:createPortfolio', createPortfolio)
    socket.on('portfolioController:updatePortfolio', updatePortfolio)
    socket.on('portfolioController:deletePortfolio', deletePortfolio)
    socket.on('portfolioController:getPortfolioImages', getPortfolioImages)

    // Portfolio Images
    socket.on('portfolioImageController:uploadImages', uploadPortfolioImages)
    socket.on('portfolioImageController:deleteImage', deletePortfolioImage)
    socket.on('portfolioImageController:getImages', getUploadedPortfolioImages)

    // Portfolio Templates
    socket.on('portfolioTemplateController:getAllPortfolioTemplates', getAllPortfolioTemplates)
    socket.on('portfolioTemplateController:getPortfolioTemplate', getPortfolioTemplate)
    socket.on('portfolioTemplateController:createPortfolioTemplate', createPortfolioTemplate)
    socket.on('portfolioTemplateController:updatePortfolioTemplate', updatePortfolioTemplate)
    socket.on('portfolioTemplateController:deletePortfolioTemplate', deletePortfolioTemplate)
    socket.on('portfolioTemplateController:clonePortfolioTemplate', clonePortfolioTemplate)

    // Term Condition Templates
    socket.on('termConditionTemplateController:getAllTermConditionTemplates', getAllTermConditionTemplates)
    socket.on('termConditionTemplateController:getTermConditionTemplate', getTermConditionTemplate)
    socket.on('termConditionTemplateController:createTermConditionTemplate', createTermConditionTemplate)
    socket.on('termConditionTemplateController:updateTermConditionTemplate', updateTermConditionTemplate)
    socket.on('termConditionTemplateController:deleteTermConditionTemplate', deleteTermConditionTemplate)

    // Term Conditions
    socket.on('termConditionController:getAllTermConditions', getAllTermConditions)
    socket.on('termConditionController:getTermCondition', getTermCondition)
    socket.on('termConditionController:createTermCondition', createTermCondition)
    socket.on('termConditionController:updateTermCondition', updateTermCondition)
    socket.on('termConditionController:deleteTermCondition', deleteTermCondition)

    // Expenses
    socket.on('expenseController:getExpenses', getExpenses)
    socket.on('expenseController:getExpense', getExpense)
    socket.on('expenseController:createExpense', createExpense)
    socket.on('expenseController:updateExpense', updateExpense)
    socket.on('expenseController:deleteExpense', deleteExpense)

    // Expense Categories
    socket.on('expenseCategoryController:getExpenseCategories', getExpenseCategories)
    socket.on('expenseCategoryController:getExpenseCategory', getExpenseCategory)
    socket.on('expenseCategoryController:createExpenseCategory', createExpenseCategory)
    socket.on('expenseCategoryController:updateExpenseCategory', updateExpenseCategory)
    socket.on('expenseCategoryController:deleteExpenseCategory', deleteExpenseCategory)

    // Rental Items
    socket.on('rentalController:getRentals', getRentals)
    socket.on('rentalController:getRental', getRental)
    socket.on('rentalController:createRental', createRental)
    socket.on('rentalController:updateRental', updateRental)
    socket.on('rentalController:deleteRental', deleteRental)

    // Rental Categories
    socket.on('rentalCategoryController:getCategories', getRentalCategories)
    socket.on('rentalCategoryController:getCategory', getRentalCategory)
    socket.on('rentalCategoryController:createCategory', createRentalCategory)
    socket.on('rentalCategoryController:updateCategory', updateRentalCategory)
    socket.on('rentalCategoryController:deleteCategory', deleteRentalCategory)

    // Rental Bookings
    socket.on('rentalBookingController:getBookings', getRentalBookings)
    socket.on('rentalBookingController:getBooking', getRentalBooking)
    socket.on('rentalBookingController:createBooking', createRentalBooking)
    socket.on('rentalBookingController:updateBooking', updateRentalBooking)
    socket.on('rentalBookingController:deleteBooking', deleteRentalBooking)
    socket.on('rentalBookingController:changeBookingStatus', changeRentalBookingStatus)
    socket.on('rentalBookingController:checkOutBooking', checkOutBooking)
    socket.on('rentalBookingController:checkInBooking', checkInBooking)
    
    // Support Tickets
    socket.on('supportTicketController:getTickets', getTickets)
    socket.on('supportTicketController:getTicket', getTicket)
    socket.on('supportTicketController:createTicket', createTicket)
    socket.on('supportTicketController:updateTicket', updateTicket)
    socket.on('supportTicketController:deleteTicket', deleteTicket)
    socket.on('supportTicketController:addComment', addComment)
    socket.on('supportTicketController:assignTicket', assignTicket)

    // Onboarding Templates
    socket.on('onboardingTemplateController:getAllOnboardingTemplates', getAllOnboardingTemplates)
    socket.on('onboardingTemplateController:getOnboardingTemplate', getOnboardingTemplate)
    socket.on('onboardingTemplateController:createOnboardingTemplate', createOnboardingTemplate)
    socket.on('onboardingTemplateController:updateOnboardingTemplate', updateOnboardingTemplate)
    socket.on('onboardingTemplateController:deleteOnboardingTemplate', deleteOnboardingTemplate)

    // Teams
    socket.on('teamController:getTeams', getTeams)
    socket.on('teamController:getTeam', getTeam)
    socket.on('teamController:createTeam', createTeam)
    socket.on('teamController:updateTeam', updateTeam)
    socket.on('teamController:deleteTeam', deleteTeam)
    socket.on('teamController:addMember', addMember)
    socket.on('teamController:removeMember', removeMember)
    socket.on('teamController:updateMemberRole', updateMemberRole)

    // Team Invites
    socket.on('teamInviteController:getTeamInvites', getTeamInvites)
    socket.on('teamInviteController:getTeamInvite', getTeamInvite)
    socket.on('teamInviteController:createTeamInvite', createTeamInvite)
    socket.on('teamInviteController:acceptTeamInvite', acceptTeamInvite)
    socket.on('teamInviteController:rejectTeamInvite', rejectTeamInvite)
    socket.on('teamInviteController:cancelTeamInvite', cancelTeamInvite)
    socket.on('teamInviteController:resendTeamInvite', resendTeamInvite)
    socket.on('teamInviteController:cleanupExpiredInvites', cleanupExpiredInvites)

    // Chat
    socket.on('chatController:getChats', getChats)
    socket.on('chatController:getChat', getChat)
    socket.on('chatController:createIndividualChat', createIndividualChat)
    socket.on('chatController:createGroupChat', createGroupChat)
    socket.on('chatController:updateChat', updateChat)
    socket.on('chatController:leaveChat', leaveChat)
    socket.on('chatController:addParticipants', addParticipants)

    // Messages
    socket.on('messageController:sendMessage', sendMessage)
    socket.on('messageController:getMessages', getMessages)
    socket.on('messageController:editMessage', editMessage)
    socket.on('messageController:deleteMessage', deleteMessage)
    socket.on('messageController:addReaction', addReaction)
    socket.on('messageController:removeReaction', removeReaction)
    socket.on('messageController:markMessagesAsRead', markMessagesAsRead)

    // Group Invites
    socket.on('groupController:inviteToGroup', inviteToGroup)
    socket.on('groupController:acceptGroupInvite', acceptGroupInvite)
    socket.on('groupController:rejectGroupInvite', rejectGroupInvite)
    socket.on('groupController:getGroupInvites', getGroupInvites)
    socket.on('groupController:cancelGroupInvite', cancelGroupInvite)

    // Admin Chat Management
    socket.on('chatController:getAllChatsAdmin', getAllChatsAdmin)
    socket.on('chatController:getChatAnalytics', getChatAnalytics)
    socket.on('messageController:adminDeleteMessage', adminDeleteMessage)
    socket.on('messageController:getMessageAnalytics', getMessageAnalytics)
  } else {
    const {
      register,
      login,
      verifyAccount,
      resendVerifyToken,
      forgotPassword,
      resetPassword,
      verify2fa,
    } = require('../controllers/auth.controller')

    const { getFrontendSubscriptionPackages } = require('../controllers/subscription/package.controller')

    const {
      createPaystackSubscription,
      upgradeFromTrial,
      subscriptionCallback,
      updateUserSubscription,
      getUserSubscriptionDetails,
      cancelUserSubscription,
    } = require('../controllers/user/user-subscription.controller')

    const {
      getTeamInviteByToken,
    } = require('../controllers/team/team-invite.controller')

    const {
      getGroupInviteByToken,
    } = require('../controllers/chat/group.controller')

    // Subscriptions Paystack
    socket.on('paystackController:createPaystackSubscription', createPaystackSubscription)
    socket.on('paystackController:upgradeFromTrial', upgradeFromTrial)
    socket.on('paystackController:subscriptionCallback', subscriptionCallback)

    // User Subscription Management
    socket.on('userSubscriptionController:updateUserSubscription', updateUserSubscription)
    socket.on('userSubscriptionController:getUserSubscriptionDetails', getUserSubscriptionDetails)
    socket.on('userSubscriptionController:cancelUserSubscription', cancelUserSubscription)



    // Public Team Invite Access
    socket.on('teamInviteController:getTeamInviteByToken', getTeamInviteByToken)

    // Public Group Invite Access
    socket.on('groupController:getGroupInviteByToken', getGroupInviteByToken)

    socket.on('authController:register', register)
    socket.on('authController:login', login)
    socket.on('authController:forgotPassword', forgotPassword)
    socket.on('authController:resetPassword', resetPassword)
    socket.on('authController:verifyAccount', verifyAccount)
    socket.on('authController:resendVerifyAccount', resendVerifyToken)
    socket.on('authController:verify2fa', verify2fa)

    socket.on('subscriptionController:getFrontendSubscriptionPackages', getFrontendSubscriptionPackages)
  }

  // Public subscription plans (available to all users)
  const { getSubscriptionPlansFromFile } = require('../controllers/subscription/package.controller')
  socket.on('subscriptionController:getSubscriptionPlansFromFile', getSubscriptionPlansFromFile)

  socket.on('disconnect', () => {
    io.emit('user disconnected')
  })
})

exports.Socket = Socket
exports.io = io
