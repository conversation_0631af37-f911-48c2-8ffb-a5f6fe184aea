const mongoose = require('mongoose')
const dotenv = require('dotenv')
const SubscriptionPackage = require('../models/subscription/package')

// Load environment variables
dotenv.config({ path: './config/config.env' })

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})

// Paystack plan codes mapping (from the API test results)
const paystackPlanCodes = {
  'Basic': {
    monthly: 'PLN_lt6ibafwu0fa26l',
    yearly: 'PLN_mmxnqegms4umb1w'
  },
  'All In': {
    monthly: 'PLN_79dz0av80r8a56v',
    yearly: 'PLN_ea7er3kx4tvfbz2'
  },
  'Production': {
    monthly: 'PLN_x01uu0iabedar51',
    yearly: 'PLN_kdvlus5vynenupv'
  },
  'No Limits': {
    monthly: 'PLN_5u5gl75qhetxa4u',
    yearly: 'PLN_4rp5wgoa8h79n4q'
  }
}

// Default subscription packages to create if none exist
const defaultPackages = [
  {
    title: 'Basic',
    description: 'A simple start for everyone',
    priceMonthly: 280,
    priceYearly: 2480,
    maxIdentities: 1,
    features: [
      '1 identity',
      'Basic templates',
      'Email support',
      'Basic analytics'
    ],
    isPopular: false,
    status: 'active'
  },
  {
    title: 'All In',
    description: 'For small to medium businesses',
    priceMonthly: 540,
    priceYearly: 4800,
    maxIdentities: 5,
    features: [
      '5 identities',
      'Premium templates',
      'Priority support',
      'Advanced analytics',
      'Custom branding'
    ],
    isPopular: true,
    status: 'active'
  },
  {
    title: 'Production',
    description: 'For growing businesses',
    priceMonthly: 1100,
    priceYearly: 9600,
    maxIdentities: 15,
    features: [
      '15 identities',
      'All templates',
      'Phone support',
      'Full analytics suite',
      'API access'
    ],
    isPopular: false,
    status: 'active'
  },
  {
    title: 'No Limits',
    description: 'For large organizations',
    priceMonthly: 2700,
    priceYearly: 24000,
    maxIdentities: -1, // Unlimited
    features: [
      'Unlimited identities',
      'Custom templates',
      'Dedicated support',
      'Enterprise analytics',
      'White-label solution'
    ],
    isPopular: false,
    status: 'active'
  }
]

async function fixPaystackPlanCodes() {
  try {
    console.log('Starting Paystack plan codes fix...')
    
    // Get all subscription packages
    let packages = await SubscriptionPackage.find({})
    console.log(`Found ${packages.length} subscription packages`)
    
    // If no packages exist, create default ones
    if (packages.length === 0) {
      console.log('No subscription packages found. Creating default packages...')
      
      for (const packageData of defaultPackages) {
        const pkg = new SubscriptionPackage({
          ...packageData,
          paystackPlanCodes: paystackPlanCodes[packageData.title] || { monthly: '', yearly: '' }
        })
        
        await pkg.save()
        console.log(`✅ Created package: ${packageData.title}`)
      }
      
      // Refresh packages list
      packages = await SubscriptionPackage.find({})
      console.log(`Created ${packages.length} subscription packages`)
    }
    
    // Update existing packages with Paystack plan codes
    for (const pkg of packages) {
      console.log(`\nProcessing package: ${pkg.title}`)
      console.log(`Current paystackPlanCodes:`, pkg.paystackPlanCodes)
      
      // Check if this package has a mapping
      if (paystackPlanCodes[pkg.title]) {
        const newCodes = paystackPlanCodes[pkg.title]
        
        // Update the package
        pkg.paystackPlanCodes = newCodes
        await pkg.save()
        
        console.log(`✅ Updated ${pkg.title} with codes:`, newCodes)
      } else {
        console.log(`⚠️  No Paystack plan codes found for: ${pkg.title}`)
      }
    }
    
    console.log('\n✅ Paystack plan codes fix completed!')
    
    // Verify the updates
    console.log('\n--- Verification ---')
    const updatedPackages = await SubscriptionPackage.find({})
    for (const pkg of updatedPackages) {
      console.log(`${pkg.title}:`, pkg.paystackPlanCodes)
    }
    
  } catch (error) {
    console.error('Error fixing Paystack plan codes:', error)
  } finally {
    mongoose.connection.close()
  }
}

// Run the fix
fixPaystackPlanCodes()
