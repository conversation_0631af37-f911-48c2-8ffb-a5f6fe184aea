const axios = require('axios')
const dotenv = require('dotenv')

// Load environment variables
dotenv.config({ path: './config/config.env' })

const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY

async function testPaystackAPI() {
  console.log('Testing Paystack API connection...')
  console.log('Using secret key:', PAYSTACK_SECRET_KEY ? `${PAYSTACK_SECRET_KEY.substring(0, 10)}...` : 'NOT SET')

  try {
    // Test 1: List plans
    console.log('\n1. Testing: List Plans')
    const plansResponse = await axios.get('https://api.paystack.co/plan', {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    })
    
    console.log('✅ Plans API working')
    console.log(`Found ${plansResponse.data.data.length} plans:`)
    plansResponse.data.data.forEach(plan => {
      console.log(`  - ${plan.name} (${plan.plan_code}): ${plan.currency}${plan.amount/100}/${plan.interval}`)
    })

    // Test 2: List subscriptions
    console.log('\n2. Testing: List Subscriptions')
    const subscriptionsResponse = await axios.get('https://api.paystack.co/subscription', {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    })
    
    console.log('✅ Subscriptions API working')
    console.log(`Found ${subscriptionsResponse.data.data.length} subscriptions`)
    
    if (subscriptionsResponse.data.data.length > 0) {
      const testSubscription = subscriptionsResponse.data.data[0]
      console.log(`Sample subscription: ${testSubscription.subscription_code} - ${testSubscription.status}`)
      
      // Test 3: Try to generate update link for first subscription
      console.log('\n3. Testing: Generate Update Link')
      try {
        const updateLinkResponse = await axios.post(
          `https://api.paystack.co/subscription/${testSubscription.subscription_code}/manage/link`,
          {},
          {
            headers: {
              Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
              'Content-Type': 'application/json',
            },
          }
        )
        
        if (updateLinkResponse.data.status) {
          console.log('✅ Update link generation working')
          console.log('Update link:', updateLinkResponse.data.data.link)
        } else {
          console.log('❌ Update link generation failed:', updateLinkResponse.data.message)
        }
      } catch (updateLinkError) {
        console.log('❌ Update link generation error:', updateLinkError.response?.data?.message || updateLinkError.message)
      }
      
      // Test 4: Try subscription disable/enable (be careful with this)
      console.log('\n4. Testing: Subscription Management (Read-only)')
      try {
        const subscriptionDetailsResponse = await axios.get(
          `https://api.paystack.co/subscription/${testSubscription.subscription_code}`,
          {
            headers: {
              Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
              'Content-Type': 'application/json',
            },
          }
        )
        
        if (subscriptionDetailsResponse.data.status) {
          console.log('✅ Subscription details retrieval working')
          const details = subscriptionDetailsResponse.data.data
          console.log(`Subscription details: ${details.customer.email} - Plan: ${details.plan.name}`)
        }
      } catch (detailsError) {
        console.log('❌ Subscription details error:', detailsError.response?.data?.message || detailsError.message)
      }
    } else {
      console.log('No subscriptions found to test with')
    }

    console.log('\n✅ Paystack API tests completed successfully!')
    
  } catch (error) {
    console.error('❌ Paystack API test failed:', error.response?.data || error.message)
    
    if (error.response?.status === 401) {
      console.error('Authentication failed. Please check your PAYSTACK_SECRET_KEY')
    }
  }
}

// Run the test
testPaystackAPI()
