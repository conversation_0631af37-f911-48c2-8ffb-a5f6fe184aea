const mongoose = require('mongoose')
const dotenv = require('dotenv')
const UserSubscription = require('../models/user/user-subscription')
const User = require('../models/user/user')
const SubscriptionPackage = require('../models/subscription/package')

// Load environment variables
dotenv.config({ path: './config/config.env' })

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})

async function checkSubscriptionStatus() {
  try {
    console.log('Checking subscription statuses...')

    const now = new Date()
    console.log('Current time:', now.toISOString())

    // Get all user subscriptions
    const allSubscriptions = await UserSubscription.find({})
      .populate('user', 'email username')
      .populate('currentPackage', 'title')

    console.log(`\nFound ${allSubscriptions.length} total subscriptions`)

    for (const subscription of allSubscriptions) {
      console.log(`\n--- User: ${subscription.user?.email || 'Unknown'} ---`)
      console.log(`Status: ${subscription.status}`)
      console.log(`Trial Ends At: ${subscription.trialEndsAt ? subscription.trialEndsAt.toISOString() : 'N/A'}`)
      console.log(`Subscription Ends At: ${subscription.endsAt ? subscription.endsAt.toISOString() : 'N/A'}`)
      console.log(`Has Used Trial: ${subscription.hasUsedTrial}`)
      console.log(`Current Package: ${subscription.currentPackage?.title || 'None'}`)

      // Calculate actual status
      let actualStatus = subscription.status
      let shouldUpdate = false

      if (subscription.status === 'trial' && subscription.trialEndsAt) {
        if (new Date(subscription.trialEndsAt) <= now) {
          actualStatus = 'expired'
          shouldUpdate = true
          console.log(`❌ TRIAL EXPIRED - Should be marked as expired`)
        } else {
          const daysRemaining = Math.ceil((new Date(subscription.trialEndsAt) - now) / (1000 * 60 * 60 * 24))
          console.log(`✅ Trial active - ${daysRemaining} days remaining`)
        }
      }

      if (subscription.status === 'active' && subscription.endsAt) {
        if (new Date(subscription.endsAt) <= now) {
          actualStatus = 'expired'
          shouldUpdate = true
          console.log(`❌ SUBSCRIPTION EXPIRED - Should be marked as expired`)
        } else {
          const daysRemaining = Math.ceil((new Date(subscription.endsAt) - now) / (1000 * 60 * 60 * 24))
          console.log(`✅ Subscription active - ${daysRemaining} days remaining`)
        }
      }

      if (shouldUpdate) {
        console.log(`🔄 Updating status from '${subscription.status}' to '${actualStatus}'`)
        subscription.status = actualStatus
        await subscription.save()
        console.log(`✅ Status updated successfully`)
      }
    }

    // Check for expired trials specifically
    const expiredTrials = await UserSubscription.find({
      status: 'trial',
      trialEndsAt: { $lte: now },
    }).populate('user', 'email')

    console.log(`\n🔍 Found ${expiredTrials.length} trials that should be expired`)

    // Check for expired subscriptions specifically
    const expiredSubscriptions = await UserSubscription.find({
      status: 'active',
      endsAt: { $lte: now },
    }).populate('user', 'email')

    console.log(`🔍 Found ${expiredSubscriptions.length} subscriptions that should be expired`)

    console.log('\n✅ Subscription status check completed!')

  } catch (error) {
    console.error('Error checking subscription status:', error)
  } finally {
    mongoose.connection.close()
  }
}

// Run the check
checkSubscriptionStatus()
