const mongoose = require('mongoose')
const dotenv = require('dotenv')
const UserSubscription = require('../models/user/user-subscription')
const User = require('../models/user/user')

// Load environment variables
dotenv.config({ path: './config/config.env' })

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})

async function testSubscriptionBlocking() {
  try {
    console.log('Testing subscription blocking scenarios...')

    // Find a user with a trial subscription
    const trialUser = await UserSubscription.findOne({ 
      status: 'trial',
      trialEndsAt: { $exists: true }
    }).populate('user', 'email username')

    if (trialUser) {
      console.log('\n--- Trial User Found ---')
      console.log(`User: ${trialUser.user?.email || 'Unknown'}`)
      console.log(`Trial Status: ${trialUser.status}`)
      console.log(`Trial Ends At: ${trialUser.trialEndsAt}`)
      console.log(`Current Time: ${new Date()}`)
      
      const now = new Date()
      const isExpired = new Date(trialUser.trialEndsAt) <= now
      console.log(`Is Trial Expired: ${isExpired}`)

      if (!isExpired) {
        console.log('\n🧪 Creating expired trial for testing...')
        
        // Set trial to expire 1 hour ago for testing
        const expiredDate = new Date(now.getTime() - (60 * 60 * 1000))
        trialUser.trialEndsAt = expiredDate
        await trialUser.save()
        
        console.log(`✅ Trial expiration set to: ${expiredDate}`)
        console.log('🚫 This user should now be blocked on the frontend')
      } else {
        console.log('✅ Trial is already expired - user should be blocked')
      }
    } else {
      console.log('No trial users found in database')
    }

    // Find users with expired status
    const expiredUsers = await UserSubscription.find({ 
      status: 'expired' 
    }).populate('user', 'email username').limit(3)

    console.log(`\n--- Found ${expiredUsers.length} Expired Users ---`)
    expiredUsers.forEach((sub, index) => {
      console.log(`${index + 1}. ${sub.user?.email || 'Unknown'} - Status: ${sub.status}`)
      if (sub.trialEndsAt) {
        console.log(`   Trial ended: ${sub.trialEndsAt}`)
      }
      if (sub.endsAt) {
        console.log(`   Subscription ended: ${sub.endsAt}`)
      }
    })

    // Test subscription status calculation
    console.log('\n--- Testing Status Calculation ---')
    const testSubscriptions = await UserSubscription.find({}).limit(5).populate('user', 'email')
    
    testSubscriptions.forEach((sub) => {
      const now = new Date()
      let calculatedStatus = sub.status
      let shouldBlock = false

      if (sub.status === 'trial' && sub.trialEndsAt) {
        if (new Date(sub.trialEndsAt) <= now) {
          calculatedStatus = 'expired'
          shouldBlock = true
        }
      }

      if (sub.status === 'active' && sub.endsAt) {
        if (new Date(sub.endsAt) <= now) {
          calculatedStatus = 'expired'
          shouldBlock = true
        }
      }

      if (sub.status === 'expired') {
        shouldBlock = true
      }

      console.log(`User: ${sub.user?.email || 'Unknown'}`)
      console.log(`  DB Status: ${sub.status} | Calculated: ${calculatedStatus} | Should Block: ${shouldBlock}`)
    })

    console.log('\n✅ Subscription blocking test completed!')
    console.log('\n📋 Frontend Testing Instructions:')
    console.log('1. Login with a user that has an expired trial')
    console.log('2. You should see a blocking modal immediately')
    console.log('3. The modal should prevent access to the dashboard')
    console.log('4. Only "Upgrade Now" should allow progression')
    console.log('5. Check browser console for subscription status logs')

  } catch (error) {
    console.error('Error testing subscription blocking:', error)
  } finally {
    mongoose.connection.close()
  }
}

// Run the test
testSubscriptionBlocking()
