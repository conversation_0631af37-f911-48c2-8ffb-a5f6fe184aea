// cron/trialChecker.js
const cron = require('node-cron')
const UserSubscription = require('../models/user/user-subscription')
const User = require('../models/user/user')
const SubscriptionPackage = require('../models/subscription/package')
const sendEmail = require('../utils/sendEmail')

// Run every hour to check for expired trials and subscriptions
cron.schedule('0 * * * *', async () => {
  console.log('Running subscription status check cron job...')

  const now = new Date()

  try {
    // Check for expired trials
    const expiredTrials = await UserSubscription.find({
      status: 'trial',
      trialEndsAt: { $lte: now },
    }).populate('user')

    console.log(`Found ${expiredTrials.length} expired trials`)

    for (const trial of expiredTrials) {
      try {
        // Mark trial as expired
        trial.status = 'expired'
        await trial.save()

        console.log(`Marked trial as expired for user ${trial.user.email}`)

        // Send trial expiration email
        if (trial.user && trial.user.email) {
          await sendEmail({
            email: trial.user.email,
            subject: 'Your QwoteZ Trial Has Expired',
            message: `
              <h2>Trial Expired</h2>
              <p>Your 14-day free trial has expired.</p>
              <p>To continue using QwoteZ, please upgrade to a paid plan.</p>
              <p><a href="https://app.qwotez.com/subscription" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Upgrade Now</a></p>
            `,
          })
        }

      } catch (error) {
        console.error(`Error processing expired trial for user ${trial.user?.email}:`, error.message)
      }
    }

    // Check for expired paid subscriptions
    const expiredSubscriptions = await UserSubscription.find({
      status: 'active',
      endsAt: { $lte: now },
    }).populate('user')

    console.log(`Found ${expiredSubscriptions.length} expired paid subscriptions`)

    for (const subscription of expiredSubscriptions) {
      try {
        // Mark subscription as expired
        subscription.status = 'expired'
        await subscription.save()

        console.log(`Marked subscription as expired for user ${subscription.user.email}`)

        // Downgrade user to basic plan or remove subscription
        const user = await User.findById(subscription.user._id)
        if (user) {
          const basicPlan = await SubscriptionPackage.findOne({ title: 'Basic' })
          if (basicPlan) {
            user.subscriptionPlan = basicPlan._id
            if (!user.maxIdentitiesOverride) {
              user.maxIdentities = basicPlan.maxIdentities
            }
          } else {
            user.subscriptionPlan = null
            if (!user.maxIdentitiesOverride) {
              user.maxIdentities = 1
            }
          }
          await user.save()
        }

        // Send subscription expiration email
        if (subscription.user && subscription.user.email) {
          await sendEmail({
            email: subscription.user.email,
            subject: 'Your QwoteZ Subscription Has Expired',
            message: `
              <h2>Subscription Expired</h2>
              <p>Your QwoteZ subscription has expired.</p>
              <p>To continue using premium features, please renew your subscription.</p>
              <p><a href="https://app.qwotez.com/subscription" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Renew Subscription</a></p>
            `,
          })
        }

      } catch (error) {
        console.error(`Error processing expired subscription for user ${subscription.user?.email}:`, error.message)
      }
    }

    console.log('Subscription status check completed')

  } catch (error) {
    console.error('Error in subscription status check cron job:', error)
  }
})
