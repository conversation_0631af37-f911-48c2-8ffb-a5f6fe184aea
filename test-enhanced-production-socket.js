const { io } = require('socket.io-client')

// Enhanced production Socket.IO test with error recovery monitoring
const testEnhancedProductionSocket = async () => {
  console.log('🚀 Testing Enhanced Production Socket.IO Configuration...')
  console.log('=' .repeat(60))

  const socket = io('https://api.qwotez.com', {
    // Production-optimized configuration matching frontend
    transports: ['polling'], // Polling only - most reliable
    upgrade: false, // Never upgrade to websocket
    forceNew: true, // Force new connection each time
    timeout: 120000, // 2 minute timeout
    reconnectionDelay: 5000, // 5 second delays
    reconnectionDelayMax: 30000, // Max 30 seconds
    reconnectionAttempts: 50, // Many attempts
    randomizationFactor: 0.3, // Less randomization
    timestampRequests: true, // Prevent caching issues
    timestampParam: 't',
    // Additional polling-specific options
    pollingTimeout: 60000, // 1 minute polling timeout
    forceBase64: false, // Don't force base64 encoding
    enablesXDR: false, // Disable XDR for IE
    // Force HTTP/1.1 to avoid HTTP/2 issues
    extraHeaders: {
      'Connection': 'keep-alive',
      'Cache-Control': 'no-cache',
    },
    auth: {
      token: 'test-token-12345',
      userId: 'test-user-67890'
    }
  })

  let connectionAttempts = 0
  let errorCount = 0
  let recoveryAttempts = 0

  // Enhanced connection monitoring
  socket.on('connect', () => {
    console.log('✅ Production: Connected successfully!')
    console.log('📍 Socket ID:', socket.id)
    console.log('🚀 Transport:', socket.io.engine.transport.name)
    console.log('🔗 URL:', socket.io.uri)
    console.log('⏱️  Connection attempts:', connectionAttempts + 1)
    
    connectionAttempts++

    // Test message sending
    console.log('📤 Production: Testing message send...')
    socket.emit('test-message', {
      message: 'Enhanced production test message',
      timestamp: new Date().toISOString(),
      attempt: connectionAttempts
    })
  })

  socket.on('connected', (data) => {
    console.log('✅ Server confirmed connection:', data)
  })

  // Enhanced error monitoring
  socket.on('connect_error', (error) => {
    errorCount++
    console.error('❌ Production: Connection error #' + errorCount + ':', error.message)
    console.log('🔧 Production error recovery activated')
    
    // Monitor error patterns
    if (error.message.includes('xhr poll error')) {
      console.log('🌐 XHR polling error detected - network instability')
    } else if (error.message.includes('timeout')) {
      console.log('⏰ Timeout error detected - extending timeouts')
    } else if (error.message.includes('transport')) {
      console.log('🚛 Transport error detected - forcing polling mode')
    }
  })

  socket.on('disconnect', (reason) => {
    console.log('🔌 Production: Disconnected -', reason)
    console.log('🌐 Network error detected - implementing recovery strategy')
    
    if (reason === 'transport error' || reason === 'transport close') {
      recoveryAttempts++
      console.log('🔧 Transport error recovery attempt #' + recoveryAttempts)
    }
  })

  socket.on('reconnect', (attemptNumber) => {
    console.log('🔄 Production: Reconnected after', attemptNumber, 'attempts')
    console.log('✅ Network recovery successful')
  })

  socket.on('reconnect_error', (error) => {
    console.error('🔄 Production: Reconnection error:', error.message)
    console.log('🌐 Network error detected - implementing recovery strategy')
  })

  socket.on('reconnect_failed', () => {
    console.error('❌ Production: Reconnection failed - all attempts exhausted')
    console.log('🌐 Network error detected - implementing recovery strategy')
  })

  socket.on('error', (error) => {
    console.error('❌ Production: General error:', error)
    console.log('🌐 Network error detected - implementing recovery strategy')
  })

  // Test message response
  socket.on('test-response', (data) => {
    console.log('📥 Production: Received response:', data)
  })

  // Monitor connection stability over time
  const stabilityTest = () => {
    let stableTime = 0
    const checkInterval = 1000 // Check every second
    const maxTestTime = 30000 // Test for 30 seconds

    const stabilityCheck = setInterval(() => {
      stableTime += checkInterval

      if (socket.connected) {
        console.log(`💓 Production: Connection stable for ${stableTime/1000}s`)
        
        // Send periodic test messages
        if (stableTime % 5000 === 0) { // Every 5 seconds
          socket.emit('stability-test', {
            message: 'Stability test message',
            timestamp: new Date().toISOString(),
            stableTime: stableTime
          })
        }
      } else {
        console.log(`⚠️  Production: Connection lost after ${stableTime/1000}s`)
      }

      if (stableTime >= maxTestTime) {
        clearInterval(stabilityCheck)
        console.log('🏁 Production: Stability test completed')
        console.log('📊 Final Statistics:')
        console.log('   - Connection attempts:', connectionAttempts)
        console.log('   - Error count:', errorCount)
        console.log('   - Recovery attempts:', recoveryAttempts)
        console.log('   - Final status:', socket.connected ? 'Connected' : 'Disconnected')
        
        socket.disconnect()
        process.exit(0)
      }
    }, checkInterval)
  }

  // Start stability test after initial connection
  socket.on('connect', () => {
    setTimeout(stabilityTest, 2000) // Wait 2 seconds after connection
  })

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted by user')
    console.log('📊 Final Statistics:')
    console.log('   - Connection attempts:', connectionAttempts)
    console.log('   - Error count:', errorCount)
    console.log('   - Recovery attempts:', recoveryAttempts)
    console.log('   - Final status:', socket.connected ? 'Connected' : 'Disconnected')
    socket.disconnect()
    process.exit(0)
  })
}

// Run the enhanced test
testEnhancedProductionSocket().catch(console.error)
