{"name": "qwote-z-admin-dashboard", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 5050", "lint": "eslint . -c .eslintrc.cjs --fix --ext .ts,.js,.cjs,.vue,.tsx,.jsx", "build:icons": "tsx src/plugins/iconify/build-icons.js", "postinstall": "npm run build:icons"}, "dependencies": {"@casl/ability": "6.7.3", "@casl/vue": "2.2.2", "@ditdot-dev/vue-flow-form": "^2.3.2", "@floating-ui/dom": "1.7.0", "@formkit/drag-and-drop": "0.5.3", "@rafaeljunioxavier/vue-quill-fix": "^1.2.4", "@sindresorhus/is": "7.0.1", "@tiptap/extension-blockquote": "^2.14.0", "@tiptap/extension-bold": "^2.14.0", "@tiptap/extension-bullet-list": "^2.14.0", "@tiptap/extension-code-block": "^2.14.0", "@tiptap/extension-heading": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-horizontal-rule": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-italic": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-ordered-list": "^2.14.0", "@tiptap/extension-strike": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@tiptap/vue-3": "^2.14.0", "@unhead/vue": "^1.11.20", "@vueuse/core": "13.1.0", "@vueuse/math": "13.1.0", "apexcharts": "4.7.0", "chart.js": "4.4.9", "cookie-es": "1.2.2", "destr": "2.0.5", "eslint-plugin-regexp": "2.7.0", "html2pdf.js": "^0.10.3", "jwt-decode": "4.0.0", "mapbox-gl": "3.11.1", "moment": "^2.30.1", "ofetch": "1.4.1", "pinia": "2.3.1", "pinia-plugin-persistedstate-2": "^2.0.30", "prismjs": "1.30.0", "quill-image-uploader": "^1.3.0", "roboto-fontface": "0.10.0", "shepherd.js": "14.5.0", "socket.io-client": "^4.8.1", "swiper": "11.2.6", "ufo": "1.6.1", "unplugin-vue-define-options": "1.5.5", "uuid": "^11.1.0", "vue": "3.5.13", "vue-chartjs": "5.3.2", "vue-draggable-next": "^2.2.1", "vue-flatpickr-component": "11.0.5", "vue-i18n": "11.1.3", "vue-prism-component": "2.0.0", "vue-router": "4.5.1", "vue-sweetalert2": "^5.0.11", "vue3-apexcharts": "1.8.0", "vue3-perfect-scrollbar": "2.0.0", "vue3-signature": "^0.2.4", "vue3-toastify": "^0.2.8", "vuetify": "3.8.3", "webfontloader": "1.6.28"}, "devDependencies": {"@antfu/eslint-config-vue": "0.43.1", "@antfu/utils": "9.2.0", "@fullcalendar/core": "6.1.17", "@fullcalendar/daygrid": "6.1.17", "@fullcalendar/interaction": "6.1.17", "@fullcalendar/list": "6.1.17", "@fullcalendar/timegrid": "6.1.17", "@fullcalendar/vue3": "6.1.17", "@iconify-json/fa": "1.2.1", "@iconify-json/mdi": "1.2.3", "@iconify-json/ri": "1.2.5", "@iconify-json/tabler": "1.2.17", "@iconify/tools": "4.1.2", "@iconify/utils": "2.3.0", "@iconify/vue": "4.3.0", "@intlify/unplugin-vue-i18n": "6.0.8", "@stylistic/stylelint-config": "2.0.0", "@stylistic/stylelint-plugin": "3.1.2", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-subscript": "^2.14.0", "@tiptap/extension-superscript": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@videojs-player/vue": "1.0.0", "@vitejs/plugin-vue": "5.2.3", "@vitejs/plugin-vue-jsx": "4.1.2", "eslint": "8.57.0", "eslint-config-airbnb-base": "15.0.0", "eslint-import-resolver-typescript": "3.10.1", "eslint-plugin-case-police": "0.7.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-promise": "6.6.0", "eslint-plugin-regex": "1.10.0", "eslint-plugin-sonarjs": "0.25.1", "eslint-plugin-unicorn": "51.0.1", "eslint-plugin-vue": "9.33.0", "postcss-html": "1.8.0", "postcss-scss": "4.0.9", "sass": "1.76.0", "shiki": "3.3.0", "stylelint": "16.14.1", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "14.0.0", "stylelint-use-logical-spec": "5.0.1", "tsx": "4.19.4", "unplugin-auto-import": "19.0.0", "unplugin-vue-components": "28.5.0", "unplugin-vue-router": "0.8.8", "video.js": "8.22.0", "vite": "6.3.4", "vite-plugin-vue-devtools": "7.7.6", "vite-plugin-vue-layouts": "0.11.0", "vite-plugin-vuetify": "2.1.1", "vite-svg-loader": "5.1.0", "vue-shepherd": "5.0.1"}, "resolutions": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7"}, "overrides": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7"}}