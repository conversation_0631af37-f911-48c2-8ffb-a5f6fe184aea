/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    'root': RouteRecordInfo<'root', '/', Record<never, never>, Record<never, never>>,
    '$error': RouteRecordInfo<'$error', '/:error(.*)', { error: ParamValue<true> }, { error: ParamValue<false> }>,
    'academies-categories': RouteRecordInfo<'academies-categories', '/academies/categories', Record<never, never>, Record<never, never>>,
    'academies-courses': RouteRecordInfo<'academies-courses', '/academies/courses', Record<never, never>, Record<never, never>>,
    'admin-dashboard': RouteRecordInfo<'admin-dashboard', '/admin/dashboard', Record<never, never>, Record<never, never>>,
    'auth2fa': RouteRecordInfo<'auth2fa', '/auth/2fa', Record<never, never>, Record<never, never>>,
    'auth-account-verification-id-token': RouteRecordInfo<'auth-account-verification-id-token', '/auth/account-verification/:id/:token', { id: ParamValue<true>, token: ParamValue<true> }, { id: ParamValue<false>, token: ParamValue<false> }>,
    'auth-components-custom-radio-subscription': RouteRecordInfo<'auth-components-custom-radio-subscription', '/auth/components/CustomRadioSubscription', Record<never, never>, Record<never, never>>,
    'auth-components-redirect-loader': RouteRecordInfo<'auth-components-redirect-loader', '/auth/components/RedirectLoader', Record<never, never>, Record<never, never>>,
    'auth-forgot-password': RouteRecordInfo<'auth-forgot-password', '/auth/forgot-password', Record<never, never>, Record<never, never>>,
    'auth-login': RouteRecordInfo<'auth-login', '/auth/login', Record<never, never>, Record<never, never>>,
    'auth-not-authorized': RouteRecordInfo<'auth-not-authorized', '/auth/not-authorized', Record<never, never>, Record<never, never>>,
    'auth-otp': RouteRecordInfo<'auth-otp', '/auth/otp', Record<never, never>, Record<never, never>>,
    'auth-register': RouteRecordInfo<'auth-register', '/auth/register', Record<never, never>, Record<never, never>>,
    'auth-reset-password-id': RouteRecordInfo<'auth-reset-password-id', '/auth/reset-password/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'auth-verify-email': RouteRecordInfo<'auth-verify-email', '/auth/verify-email', Record<never, never>, Record<never, never>>,
    'bookings': RouteRecordInfo<'bookings', '/bookings', Record<never, never>, Record<never, never>>,
    'bookings-templates': RouteRecordInfo<'bookings-templates', '/bookings/templates', Record<never, never>, Record<never, never>>,
    'business-management-identities': RouteRecordInfo<'business-management-identities', '/business-management/identities', Record<never, never>, Record<never, never>>,
    'business-management-preferences-banking-detail': RouteRecordInfo<'business-management-preferences-banking-detail', '/business-management/preferences/banking-detail', Record<never, never>, Record<never, never>>,
    'business-management-preferences-currency': RouteRecordInfo<'business-management-preferences-currency', '/business-management/preferences/currency', Record<never, never>, Record<never, never>>,
    'business-management-rates-agent-rates': RouteRecordInfo<'business-management-rates-agent-rates', '/business-management/rates/agent-rates', Record<never, never>, Record<never, never>>,
    'business-management-rates-components-rate-item': RouteRecordInfo<'business-management-rates-components-rate-item', '/business-management/rates/components/RateItem', Record<never, never>, Record<never, never>>,
    'business-management-rates-components-rate-repeater': RouteRecordInfo<'business-management-rates-components-rate-repeater', '/business-management/rates/components/RateRepeater', Record<never, never>, Record<never, never>>,
    'business-management-rates-crew-rates': RouteRecordInfo<'business-management-rates-crew-rates', '/business-management/rates/crew-rates', Record<never, never>, Record<never, never>>,
    'business-management-rates-staff-rates': RouteRecordInfo<'business-management-rates-staff-rates', '/business-management/rates/staff-rates', Record<never, never>, Record<never, never>>,
    'business-tools-calendars': RouteRecordInfo<'business-tools-calendars', '/business-tools/calendars', Record<never, never>, Record<never, never>>,
    'business-tools-kanban-boards': RouteRecordInfo<'business-tools-kanban-boards', '/business-tools/kanban-boards', Record<never, never>, Record<never, never>>,
    'business-tools-mood-boards': RouteRecordInfo<'business-tools-mood-boards', '/business-tools/mood-boards', Record<never, never>, Record<never, never>>,
    'clients': RouteRecordInfo<'clients', '/clients', Record<never, never>, Record<never, never>>,
    'clients-id': RouteRecordInfo<'clients-id', '/clients/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'clients-create': RouteRecordInfo<'clients-create', '/clients/create', Record<never, never>, Record<never, never>>,
    'clients-invites': RouteRecordInfo<'clients-invites', '/clients/invites', Record<never, never>, Record<never, never>>,
    'clients-onboarding': RouteRecordInfo<'clients-onboarding', '/clients/onboarding', Record<never, never>, Record<never, never>>,
    'clients-onboarding-id-edit': RouteRecordInfo<'clients-onboarding-id-edit', '/clients/onboarding/:id/edit', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'clients-onboarding-create': RouteRecordInfo<'clients-onboarding-create', '/clients/onboarding/create', Record<never, never>, Record<never, never>>,
    'clients-onboarding-templates': RouteRecordInfo<'clients-onboarding-templates', '/clients/onboarding/templates', Record<never, never>, Record<never, never>>,
    'collaborators': RouteRecordInfo<'collaborators', '/collaborators', Record<never, never>, Record<never, never>>,
    'collaborators-invites': RouteRecordInfo<'collaborators-invites', '/collaborators/invites', Record<never, never>, Record<never, never>>,
    'communications-chats': RouteRecordInfo<'communications-chats', '/communications/chats', Record<never, never>, Record<never, never>>,
    'components-builders-builder-canvas': RouteRecordInfo<'components-builders-builder-canvas', '/components/builders/BuilderCanvas', Record<never, never>, Record<never, never>>,
    'components-builders-contract-builders-contract-builder': RouteRecordInfo<'components-builders-contract-builders-contract-builder', '/components/builders/contract-builders/ContractBuilder', Record<never, never>, Record<never, never>>,
    'components-builders-field-editor': RouteRecordInfo<'components-builders-field-editor', '/components/builders/FieldEditor', Record<never, never>, Record<never, never>>,
    'components-builders-field-palette': RouteRecordInfo<'components-builders-field-palette', '/components/builders/FieldPalette', Record<never, never>, Record<never, never>>,
    'components-builders-form-renderer': RouteRecordInfo<'components-builders-form-renderer', '/components/builders/FormRenderer', Record<never, never>, Record<never, never>>,
    'components-builders-rate-card-builder-rate-card-builder': RouteRecordInfo<'components-builders-rate-card-builder-rate-card-builder', '/components/builders/rate-card-builder/RateCardBuilder', Record<never, never>, Record<never, never>>,
    'components-builders-rate-card-builder-rate-card-renderer': RouteRecordInfo<'components-builders-rate-card-builder-rate-card-renderer', '/components/builders/rate-card-builder/RateCardRenderer', Record<never, never>, Record<never, never>>,
    'components-builders-step-form-builder-step-field-editor': RouteRecordInfo<'components-builders-step-form-builder-step-field-editor', '/components/builders/step-form-builder/StepFieldEditor', Record<never, never>, Record<never, never>>,
    'components-builders-step-form-builder-step-field-palette': RouteRecordInfo<'components-builders-step-form-builder-step-field-palette', '/components/builders/step-form-builder/StepFieldPalette', Record<never, never>, Record<never, never>>,
    'components-builders-step-form-builder-step-form-renderer': RouteRecordInfo<'components-builders-step-form-builder-step-form-renderer', '/components/builders/step-form-builder/StepFormRenderer', Record<never, never>, Record<never, never>>,
    'components-builders-step-form-builder-stepper-builder-canvas': RouteRecordInfo<'components-builders-step-form-builder-stepper-builder-canvas', '/components/builders/step-form-builder/StepperBuilderCanvas', Record<never, never>, Record<never, never>>,
    'components-builders-step-form-builder-stepper-form-builder': RouteRecordInfo<'components-builders-step-form-builder-stepper-form-builder', '/components/builders/step-form-builder/StepperFormBuilder', Record<never, never>, Record<never, never>>,
    'components-builders-terms-builder-terms-builder': RouteRecordInfo<'components-builders-terms-builder-terms-builder', '/components/builders/terms-builder/TermsBuilder', Record<never, never>, Record<never, never>>,
    'components-builders-terms-builder-terms-builder-canvas': RouteRecordInfo<'components-builders-terms-builder-terms-builder-canvas', '/components/builders/terms-builder/TermsBuilderCanvas', Record<never, never>, Record<never, never>>,
    'components-builders-terms-builder-terms-builder-palette': RouteRecordInfo<'components-builders-terms-builder-terms-builder-palette', '/components/builders/terms-builder/TermsBuilderPalette', Record<never, never>, Record<never, never>>,
    'components-builders-terms-builder-terms-field-editor': RouteRecordInfo<'components-builders-terms-builder-terms-field-editor', '/components/builders/terms-builder/TermsFieldEditor', Record<never, never>, Record<never, never>>,
    'components-invoice-invoice-add-payment-drawer': RouteRecordInfo<'components-invoice-invoice-add-payment-drawer', '/components/invoice/InvoiceAddPaymentDrawer', Record<never, never>, Record<never, never>>,
    'components-invoice-invoice-editable': RouteRecordInfo<'components-invoice-invoice-editable', '/components/invoice/InvoiceEditable', Record<never, never>, Record<never, never>>,
    'components-invoice-invoice-product-edit': RouteRecordInfo<'components-invoice-invoice-product-edit', '/components/invoice/InvoiceProductEdit', Record<never, never>, Record<never, never>>,
    'components-invoice-invoice-send-invoice-drawer': RouteRecordInfo<'components-invoice-invoice-send-invoice-drawer', '/components/invoice/InvoiceSendInvoiceDrawer', Record<never, never>, Record<never, never>>,
    'components-kanban-badge': RouteRecordInfo<'components-kanban-badge', '/components/kanban/Badge', Record<never, never>, Record<never, never>>,
    'components-kanban-task-card': RouteRecordInfo<'components-kanban-task-card', '/components/kanban/TaskCard', Record<never, never>, Record<never, never>>,
    'components-prod-categories-estimate': RouteRecordInfo<'components-prod-categories-estimate', '/components/prod-categories/Estimate', Record<never, never>, Record<never, never>>,
    'components-prod-categories-ligs': RouteRecordInfo<'components-prod-categories-ligs', '/components/prod-categories/Ligs', Record<never, never>, Record<never, never>>,
    'components-prod-categories-line-item-repeater': RouteRecordInfo<'components-prod-categories-line-item-repeater', '/components/prod-categories/LineItemRepeater', Record<never, never>, Record<never, never>>,
    'contracts': RouteRecordInfo<'contracts', '/contracts', Record<never, never>, Record<never, never>>,
    'contracts-id-edit': RouteRecordInfo<'contracts-id-edit', '/contracts/:id/edit', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'contracts-create': RouteRecordInfo<'contracts-create', '/contracts/create', Record<never, never>, Record<never, never>>,
    'contracts-templates': RouteRecordInfo<'contracts-templates', '/contracts/templates', Record<never, never>, Record<never, never>>,
    'emails': RouteRecordInfo<'emails', '/emails', Record<never, never>, Record<never, never>>,
    'emails-templates': RouteRecordInfo<'emails-templates', '/emails/templates', Record<never, never>, Record<never, never>>,
    'estimate': RouteRecordInfo<'estimate', '/estimate', Record<never, never>, Record<never, never>>,
    'estimates': RouteRecordInfo<'estimates', '/estimates', Record<never, never>, Record<never, never>>,
    'estimates-id': RouteRecordInfo<'estimates-id', '/estimates/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'events': RouteRecordInfo<'events', '/events', Record<never, never>, Record<never, never>>,
    'events-templates': RouteRecordInfo<'events-templates', '/events/templates', Record<never, never>, Record<never, never>>,
    'invoice': RouteRecordInfo<'invoice', '/invoice', Record<never, never>, Record<never, never>>,
    'invoice-preview-id': RouteRecordInfo<'invoice-preview-id', '/invoice-preview/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'invoices': RouteRecordInfo<'invoices', '/invoices', Record<never, never>, Record<never, never>>,
    'invoices-id': RouteRecordInfo<'invoices-id', '/invoices/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'invoices-templates': RouteRecordInfo<'invoices-templates', '/invoices/templates', Record<never, never>, Record<never, never>>,
    'jobs': RouteRecordInfo<'jobs', '/jobs', Record<never, never>, Record<never, never>>,
    'jobs-id': RouteRecordInfo<'jobs-id', '/jobs/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'jobs-create': RouteRecordInfo<'jobs-create', '/jobs/create', Record<never, never>, Record<never, never>>,
    'line-item-groups': RouteRecordInfo<'line-item-groups', '/line-item-groups', Record<never, never>, Record<never, never>>,
    'line-item-groups-categories': RouteRecordInfo<'line-item-groups-categories', '/line-item-groups/categories', Record<never, never>, Record<never, never>>,
    'notifications-notifications': RouteRecordInfo<'notifications-notifications', '/notifications/notifications', Record<never, never>, Record<never, never>>,
    'notifications-system-notifications': RouteRecordInfo<'notifications-system-notifications', '/notifications/system-notifications', Record<never, never>, Record<never, never>>,
    'onboarding': RouteRecordInfo<'onboarding', '/onboarding', Record<never, never>, Record<never, never>>,
    'portfolios': RouteRecordInfo<'portfolios', '/portfolios', Record<never, never>, Record<never, never>>,
    'portfolios-components-builder': RouteRecordInfo<'portfolios-components-builder', '/portfolios/components/Builder', Record<never, never>, Record<never, never>>,
    'portfolios-components-component-renderer': RouteRecordInfo<'portfolios-components-component-renderer', '/portfolios/components/ComponentRenderer', Record<never, never>, Record<never, never>>,
    'portfolios-components-elements-crbutton': RouteRecordInfo<'portfolios-components-elements-crbutton', '/portfolios/components/elements/CRButton', Record<never, never>, Record<never, never>>,
    'portfolios-components-elements-crcheckbox': RouteRecordInfo<'portfolios-components-elements-crcheckbox', '/portfolios/components/elements/CRCheckbox', Record<never, never>, Record<never, never>>,
    'portfolios-components-elements-crgrid': RouteRecordInfo<'portfolios-components-elements-crgrid', '/portfolios/components/elements/CRGrid', Record<never, never>, Record<never, never>>,
    'portfolios-components-elements-crmasonry': RouteRecordInfo<'portfolios-components-elements-crmasonry', '/portfolios/components/elements/CRMasonry', Record<never, never>, Record<never, never>>,
    'portfolios-components-elements-crradio': RouteRecordInfo<'portfolios-components-elements-crradio', '/portfolios/components/elements/CRRadio', Record<never, never>, Record<never, never>>,
    'portfolios-page-builder': RouteRecordInfo<'portfolios-page-builder', '/portfolios/page-builder', Record<never, never>, Record<never, never>>,
    'portfolios-templates': RouteRecordInfo<'portfolios-templates', '/portfolios/templates', Record<never, never>, Record<never, never>>,
    'previews-client-id': RouteRecordInfo<'previews-client-id', '/previews/client/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'previews-client-components-invoice-add-payment-drawer': RouteRecordInfo<'previews-client-components-invoice-add-payment-drawer', '/previews/client/components/InvoiceAddPaymentDrawer', Record<never, never>, Record<never, never>>,
    'previews-client-components-invoice-send-invoice-drawer': RouteRecordInfo<'previews-client-components-invoice-send-invoice-drawer', '/previews/client/components/InvoiceSendInvoiceDrawer', Record<never, never>, Record<never, never>>,
    'production': RouteRecordInfo<'production', '/production', Record<never, never>, Record<never, never>>,
    'productions': RouteRecordInfo<'productions', '/productions', Record<never, never>, Record<never, never>>,
    'productions-id': RouteRecordInfo<'productions-id', '/productions/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'questionnaires': RouteRecordInfo<'questionnaires', '/questionnaires', Record<never, never>, Record<never, never>>,
    'questionnaires-builder': RouteRecordInfo<'questionnaires-builder', '/questionnaires/builder', Record<never, never>, Record<never, never>>,
    'quotation': RouteRecordInfo<'quotation', '/quotation', Record<never, never>, Record<never, never>>,
    'quotes': RouteRecordInfo<'quotes', '/quotes', Record<never, never>, Record<never, never>>,
    'quotes-id': RouteRecordInfo<'quotes-id', '/quotes/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'quotes-invoices': RouteRecordInfo<'quotes-invoices', '/quotes/invoices', Record<never, never>, Record<never, never>>,
    'quotes-invoices-id': RouteRecordInfo<'quotes-invoices-id', '/quotes/invoices/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'quotes-templates': RouteRecordInfo<'quotes-templates', '/quotes/templates', Record<never, never>, Record<never, never>>,
    'rate-cards': RouteRecordInfo<'rate-cards', '/rate-cards', Record<never, never>, Record<never, never>>,
    'rate-cards-template': RouteRecordInfo<'rate-cards-template', '/rate-cards/template', Record<never, never>, Record<never, never>>,
    'rate-cards-templates': RouteRecordInfo<'rate-cards-templates', '/rate-cards/templates', Record<never, never>, Record<never, never>>,
    'referrals': RouteRecordInfo<'referrals', '/referrals', Record<never, never>, Record<never, never>>,
    'referrals-discounts': RouteRecordInfo<'referrals-discounts', '/referrals/discounts', Record<never, never>, Record<never, never>>,
    'referrals-fees': RouteRecordInfo<'referrals-fees', '/referrals/fees', Record<never, never>, Record<never, never>>,
    'referrals-links': RouteRecordInfo<'referrals-links', '/referrals/links', Record<never, never>, Record<never, never>>,
    'teams': RouteRecordInfo<'teams', '/teams', Record<never, never>, Record<never, never>>,
    'teams-invites': RouteRecordInfo<'teams-invites', '/teams/invites', Record<never, never>, Record<never, never>>,
    'terms-and-conditions': RouteRecordInfo<'terms-and-conditions', '/terms-and-conditions', Record<never, never>, Record<never, never>>,
    'terms-and-conditions-advance-invoice-terms': RouteRecordInfo<'terms-and-conditions-advance-invoice-terms', '/terms-and-conditions/advance-invoice-terms', Record<never, never>, Record<never, never>>,
    'terms-and-conditions-custom-usage-licenses': RouteRecordInfo<'terms-and-conditions-custom-usage-licenses', '/terms-and-conditions/custom-usage-licenses', Record<never, never>, Record<never, never>>,
    'terms-and-conditions-estimate-terms': RouteRecordInfo<'terms-and-conditions-estimate-terms', '/terms-and-conditions/estimate-terms', Record<never, never>, Record<never, never>>,
    'terms-and-conditions-invoice-terms': RouteRecordInfo<'terms-and-conditions-invoice-terms', '/terms-and-conditions/invoice-terms', Record<never, never>, Record<never, never>>,
    'terms-and-conditions-production-terms': RouteRecordInfo<'terms-and-conditions-production-terms', '/terms-and-conditions/production-terms', Record<never, never>, Record<never, never>>,
    'terms-and-conditions-quote-terms': RouteRecordInfo<'terms-and-conditions-quote-terms', '/terms-and-conditions/quote-terms', Record<never, never>, Record<never, never>>,
    'terms-and-conditions-templates': RouteRecordInfo<'terms-and-conditions-templates', '/terms-and-conditions/templates', Record<never, never>, Record<never, never>>,
    'user-profile': RouteRecordInfo<'user-profile', '/user-profile', Record<never, never>, Record<never, never>>,
    'user-profile-components-profile': RouteRecordInfo<'user-profile-components-profile', '/user-profile/components/profile', Record<never, never>, Record<never, never>>,
    'user-profile-components-profile-about': RouteRecordInfo<'user-profile-components-profile-about', '/user-profile/components/profile/About', Record<never, never>, Record<never, never>>,
    'user-profile-components-profile-activity-timeline': RouteRecordInfo<'user-profile-components-profile-activity-timeline', '/user-profile/components/profile/ActivityTimeline', Record<never, never>, Record<never, never>>,
    'user-profile-components-profile-teams': RouteRecordInfo<'user-profile-components-profile-teams', '/user-profile/components/profile/Teams', Record<never, never>, Record<never, never>>,
    'user-profile-components-team': RouteRecordInfo<'user-profile-components-team', '/user-profile/components/team', Record<never, never>, Record<never, never>>,
    'user-profile-components-user-profile-header': RouteRecordInfo<'user-profile-components-user-profile-header', '/user-profile/components/UserProfileHeader', Record<never, never>, Record<never, never>>,
    'user-settings': RouteRecordInfo<'user-settings', '/user-settings', Record<never, never>, Record<never, never>>,
    'user-settings-components-account-settings-account': RouteRecordInfo<'user-settings-components-account-settings-account', '/user-settings/components/AccountSettingsAccount', Record<never, never>, Record<never, never>>,
    'user-settings-components-account-settings-billing-and-plans': RouteRecordInfo<'user-settings-components-account-settings-billing-and-plans', '/user-settings/components/AccountSettingsBillingAndPlans', Record<never, never>, Record<never, never>>,
    'user-settings-components-account-settings-connections': RouteRecordInfo<'user-settings-components-account-settings-connections', '/user-settings/components/AccountSettingsConnections', Record<never, never>, Record<never, never>>,
    'user-settings-components-account-settings-notification': RouteRecordInfo<'user-settings-components-account-settings-notification', '/user-settings/components/AccountSettingsNotification', Record<never, never>, Record<never, never>>,
    'user-settings-components-account-settings-security': RouteRecordInfo<'user-settings-components-account-settings-security', '/user-settings/components/AccountSettingsSecurity', Record<never, never>, Record<never, never>>,
    'user-settings-components-billing-history-table': RouteRecordInfo<'user-settings-components-billing-history-table', '/user-settings/components/BillingHistoryTable', Record<never, never>, Record<never, never>>,
  }
}
