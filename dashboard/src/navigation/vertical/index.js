// TODO: Modularize menu action: ['', ''] subject: ['', '']
export default [
  {
    title: 'Admin Dasboard',
    to: { name: 'admin-dashboard' },
    icon: { icon: 'tabler-smart-home' },
    action: 'manage',
    subject: 'all',
  },
  {
    title: 'User Management',
    to: { name: 'admin-users' },
    icon: { icon: 'tabler-users' },
    action: 'manage',
    subject: 'all',
  },
  {
    title: 'Identity Management',
    to: { name: 'admin-identity-management' },
    icon: { icon: 'tabler-id-badge' },
    action: 'manage',
    subject: 'all',
  },
  {
    title: 'Chat Management',
    to: { name: 'admin-chat-management' },
    icon: { icon: 'tabler-messages' },
    action: 'manage',
    subject: 'all',
  },
  { heading: 'Dashboard',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Home',
    to: { name: 'root' },
    icon: { icon: 'tabler-smart-home' },
    action: 'read',
    subject: 'standard',
  },
  {
    heading: 'Jobs',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Jobs',
    to: { name: 'jobs' },
    icon: { icon: 'mdi-tie' },
    action: 'read',
    subject: 'standard',
  },
  {
    heading: 'Quotes & Invoices',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Quotes',
    to: { name: 'quotes' },
    icon: { icon: 'tabler-file-invoice-filled' },
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Invoices',
    to: { name: 'quotes-invoices' },
    icon: { icon: 'tabler-file-invoice' },
    action: 'read',
    subject: 'standard',
  },
  {
    heading: 'Productions',
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Productions',
    icon: { icon: 'tabler-box' },
    children: [
      {
        title: 'Estimates',
        to: { name: 'estimates' },
        icon: { icon: 'tabler-box-margin' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Productions',
        to: { name: 'productions' },
        icon: { icon: 'tabler-box-margin' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Invoices',
        to: { name: 'invoices' },
        icon: { icon: 'tabler-box-margin' },
        action: 'read',
        subject: 'professional',
      },
    ],
  },

  /*{ heading: 'Academy' },
          {
            title: 'Academy',
            icon: { icon: 'tabler-school' },
            children: [
              {
                title: 'Categories',
                to: { name: 'academies-categories' },
                icon: { icon: 'tabler-category-2' },
                action: 'read',
                subject: 'professional',
              },
              {
                title: 'Courses',
                to: { name: 'academies-courses' },
                icon: { icon: 'tabler-certificate' },
                action: 'read',
                subject: 'professional',
              },
            ],
          },*/
  {
    heading: 'Business Management',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Expenses',
    to: { name: 'expenses' },
    icon: { icon: 'tabler-receipt' },
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Preferences',
    icon: { icon: 'tabler-settings-cog' },
    children: [
      {
        title: 'Bank Accounts',
        to: { name: 'business-management-preferences-banking-detail' },
        icon: { icon: 'tabler-businessplan' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Currencies',
        to: { name: 'business-management-preferences-currency' },
        icon: { icon: 'tabler-settings-dollar' },
        action: 'read',
        subject: 'professional',
      },
    ],
  },
  {
    title: 'Identities',
    icon: { icon: 'tabler-building-skyscraper' },
    children: [
      {
        title: 'Identities',
        to: { name: 'business-management-identities' },
        icon: { icon: 'tabler-user-hexagon' },
        action: 'read',
        subject: 'standard',
      },
    ],
  },
  {
    title: 'Contracts',
    icon: { icon: 'tabler-contract' },
    children: [
      {
        title: 'Contracts',
        to: { name: 'contracts' },
        icon: { icon: 'tabler-contract' },
        action: 'read',
        subject: 'standard',
      },
      {
        title: 'Templates',
        to: { name: 'contracts-templates' },
        icon: { icon: 'tabler-layout-grid-add' },
        action: 'read',
        subject: 'standard',
      },
    ],
  },
  {
    title: 'Rates',
    icon: { icon: 'tabler-user-star' },
    children: [
      {
        title: 'Agent Rates',
        to: { name: 'business-management-rates-agent-rates' },
        icon: { icon: 'tabler-report-money' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Crew Rates',
        to: { name: 'business-management-rates-crew-rates' },
        icon: { icon: 'tabler-user-dollar' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Staff Rates',
        to: { name: 'business-management-rates-staff-rates' },
        icon: { icon: 'tabler-cash-register' },
        action: 'read',
        subject: 'professional',
      },
    ],
  },
  {
    heading: 'Teams',
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Teams',
    to: { name: 'teams' },
    icon: { icon: 'tabler-users' },
    action: 'read',
    subject: 'professional',
  },
  {
    heading: 'Clients',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Clients',
    icon: { icon: 'tabler-accessible' },
    children: [
      {
        title: 'Clients',
        to: { name: 'clients' },
        icon: { icon: 'tabler-accessible' },
        action: 'read',
        subject: 'standard',
      },
      {
        title: 'Invites',
        to: { name: 'clients-invites' },
        icon: { icon: 'tabler-user-down' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Client Onboarding',
        to: { name: 'clients-onboarding' },
        icon: { icon: 'tabler-keyboard-hide' },
        action: 'read',
        subject: 'standard',
      },
      {
        title: 'Onboarding Templates',
        to: { name: 'clients-onboarding-templates' },
        icon: { icon: 'tabler-layout-grid-add' },
        action: 'read',
        subject: 'professional',
      },
    ],
  },
  {
    heading: 'Terms & Conditions',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Terms & Conditions',
    to: { name: 'terms-and-conditions' },
    icon: { icon: 'ri-layout-2-line' },
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Ts & Cs Templates',
    to: { name: 'terms-and-conditions-templates' },
    icon: { icon: 'tabler-layout-grid-add' },
    action: 'read',
    subject: 'professional',
  },
  { heading: 'Communications' },
  {
    title: 'Chat',
    to: { name: 'communications-chats' },
    icon: { icon: 'tabler-message-2-heart' },
    action: 'read',
    subject: 'professional',
  },
  {
    heading: 'Questionnaires',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Questionnaires',
    icon: { icon: 'tabler-mail-question' },
    children: [
      {
        title: 'Questionnaires',
        to: { name: 'questionnaires' },
        icon: { icon: 'tabler-pencil-question' },
        action: 'read',
        subject: 'standard',
      },
    ],
  },
  {
    heading: 'Rate Cards',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Rate Cards',
    icon: { icon: 'tabler-cards' },
    children: [
      {
        title: 'Rate Cards',
        to: { name: 'rate-cards' },
        icon: { icon: 'tabler-credit-card-pay' },
        action: 'read',
        subject: 'standard',
      },
    ],
  },

  {
    heading: 'Portfolios',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Portfolios',
    icon: { icon: 'tabler-layout-board' },
    to: { name: 'portfolios' },
    action: 'read',
    subject: 'standard',
  },

  {
    heading: 'Teams',
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Teams',
    to: { name: 'teams' },
    icon: { icon: 'tabler-users' },
    action: 'read',
    subject: 'professional',
  },

  {
    heading: 'Business Tools',
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Kanban Boards',
    to: { name: 'business-tools-kanban-boards' },
    icon: { icon: 'tabler-layout-dashboard-filled' },
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Mood Boards',
    to: { name: 'business-tools-mood-boards' },
    icon: { icon: 'tabler-layout-board-split' },
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Calendars',
    to: { name: 'business-tools-calendars' },
    icon: { icon: 'tabler-calendar-week' },
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Todo Lists',
    icon: { icon: 'tabler-list-check' },
    children: [
      {
        title: 'Checklists',
        to: { name: 'business-tools-todo-checklists' },
        icon: { icon: 'tabler-checklist' },
        action: 'read',
        subject: 'professional',
      },
    ],
  },
  {
    heading: 'Rental Management',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Rentals',
    icon: { icon: 'tabler-package' },
    children: [
      {
        title: 'Dashboard',
        to: { name: 'rentals' },
        icon: { icon: 'tabler-dashboard' },
        action: 'read',
        subject: 'standard',
      },
      {
        title: 'Categories',
        to: { name: 'rentals-categories' },
        icon: { icon: 'tabler-category' },
        action: 'read',
        subject: 'standard',
      },
      {
        title: 'Items',
        to: { name: 'rentals-items' },
        icon: { icon: 'tabler-box' },
        action: 'read',
        subject: 'standard',
      },
      {
        title: 'Bookings',
        to: { name: 'rentals-bookings' },
        icon: { icon: 'tabler-calendar-check' },
        action: 'read',
        subject: 'standard',
      },
    ],
  },
  {
    heading: 'Booking & Events',
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Bookings',
    to: { name: 'bookings' },
    icon: { icon: 'tabler-calendar-heart' },
    action: 'read',
    subject: 'professional',

  },
  {
    title: 'Events',
    to: { name: 'events' },
    icon: { icon: 'tabler-calendar-event' },
    action: 'read',
    subject: 'professional',

  },
  {
    heading: 'Collaborators',
    action: 'read',
    subject: 'professional',
  },
  {
    title: 'Collaborators',
    icon: { icon: 'tabler-replace-user' },
    children: [
      {
        title: 'Collaborators',
        to: { name: 'collaborators' },
        icon: { icon: 'tabler-replace-user' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Invites',
        to: { name: 'collaborators-invites' },
        icon: { icon: 'tabler-user-down' },
        action: 'read',
        subject: 'professional',
      },
    ],
  },
  {
    heading: 'Support',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Support Tickets',
    icon: { icon: 'tabler-ticket' },
    children: [
      {
        title: 'All Tickets',
        to: { name: 'support' },
        icon: { icon: 'tabler-list' },
        action: 'read',
        subject: 'standard',
      },
      {
        title: 'Create Ticket',
        to: { name: 'support-create' },
        icon: { icon: 'tabler-plus' },
        action: 'read',
        subject: 'standard',
      },
    ],
  },
  {
    heading: 'Referrals',
    action: 'read',
    subject: 'standard',
  },
  {
    title: 'Affiliate',
    icon: { icon: 'tabler-message-user' },
    children: [
      {
        title: 'Referrals',
        to: { name: 'referrals' },
        icon: { icon: 'tabler-credit-card-refund' },
        action: 'read',
        subject: 'standard',
      },
      {
        title: 'Discounts',
        to: { name: 'referrals-discounts' },
        icon: { icon: 'tabler-shopping-bag-discount' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Fees',
        to: { name: 'referrals-fees' },
        icon: { icon: 'tabler-receipt-dollar' },
        action: 'read',
        subject: 'professional',
      },
      {
        title: 'Links',
        to: { name: 'referrals-links' },
        icon: { icon: 'tabler-link' },
        action: 'read',
        subject: 'standard',
      },
    ],
  },
]
