<script setup>
import { useTheme } from 'vuetify'
import ScrollToTop from '@core/components/ScrollToTop.vue'
import initCore from '@core/initCore'
import {
  initConfigStore,
  useConfigStore,
} from '@core/stores/config'
import { hexToRgb } from '@core/utils/colorConverter'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket.js'

const { global } = useTheme()
const store = useSocketStore()

const { user } = storeToRefs(store)

// ℹ️ Sync current theme with initial loader theme
initCore()
initConfigStore()

const configStore = useConfigStore()

onMounted(() => {
  if (user.value) {
    socket.emit('user-online', user.value._id)

    socket.on('notifications', notifications => {
      console.log('Received notifications:', notifications)

      // Optional: store them in a Vue store or show a toast
    })
  }
})

watch(user, () => {
  console.log('User changed:', user.value._id)
})
</script>

<template>
  <VLocaleProvider :rtl="configStore.isAppRTL">
    <!-- ℹ️ This is required to set the background color of active nav link based on currently active global theme's primary -->
    <VApp :style="`--v-global-theme-primary: ${hexToRgb(global.current.value.colors.primary)}`">
      <RouterView />

      <ScrollToTop />
    </VApp>
  </VLocaleProvider>
</template>
