import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketKanbanStore = defineStore('SocketKanban', () => {
  const kanbans = ref(null)
  const kanban = ref(null)

  function $reset() {
    kanbans.value = null
    kanban.value = null
  }

  function getAllKanbans(data) {
    socket.emit('kanbanController:getAllKanbans', data)
  }

  function getKanban(data) {
    socket.emit('kanbanController:getKanban', data)
  }

  function createKanban(data) {
    socket.emit('kanbanController:createKanban', data)
  }

  function updateKanban(data) {
    socket.emit('kanbanController:updateKanban', data)
  }

  function deleteKanban(data) {
    socket.emit('kanbanController:deleteKanban', data)
  }

  function deleteKanbanBoard(data) {
    socket.emit('kanbanController:deleteKanbanBoard', data)
  }

  function addItemToK<PERSON>ban(data) {
    socket.emit('kanbanController:addItemToKanban', data)
  }

  function removeItemFromKanban(data) {
    socket.emit('kanbanController:removeItemFromKanban', data)
  }

  function updateItemState(data) {
    socket.emit('kanbanController:updateItemState', data)
  }

  function updateBoardState(data) {
    socket.emit('kanbanController:updateBoardState', data)
  }

  function addBoard(data) {
    socket.emit('kanbanController:addBoard', data)
  }

  function renameBoard(data) {
    socket.emit('kanbanController:renameBoard', data)
  }

  function updateItem(data) {
    socket.emit('kanbanController:updateItem', data)
  }

  return {
    kanbans,
    kanban,
    $reset,
    getAllKanbans,
    getKanban,
    createKanban,
    updateKanban,
    deleteKanban,
    deleteKanbanBoard,
    addItemToKanban,
    removeItemFromKanban,
    updateItemState,
    updateBoardState,
    addBoard,
    renameBoard,
    updateItem,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
