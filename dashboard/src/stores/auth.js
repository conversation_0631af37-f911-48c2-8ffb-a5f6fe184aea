import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketStore = defineStore('Socket', () => {
  const token = ref(null)
  const user = ref(null)
  const sessions = ref(null)
  const recentDevices = ref(null)
  const selectedNotification = ref(null)
  const notificationSettingsId = ref(null)

  function $reset() {
    token.value = null
    user.value = null
    sessions.value = null
    recentDevices.value = null
    selectedNotification.value = null
    notificationSettingsId.value = null
  }

  function login(data) {
    socket.emit('authController:login', {
      email: data.email,
      password: data.password,
    })
  }

  function register(data) {
    console.log({ data })
    socket.emit('authController:register', data)
  }

  function verifyAccount(data) {
    socket.emit('authController:verifyAccount', data)
  }

  function resendVerifyAccount(data) {
    socket.emit('authController:resendVerifyAccount', data)
  }

  function logout(data) {
    console.log('logout', { data })
    socket.emit('authController:logout', data)
  }

  function getMe(data) {
    socket.emit('authController:getMe', data)
  }

  function forgotPassword(data) {
    console.log('forgotPassword', { data })
    socket.emit('authController:forgotPassword', data)
  }

  function resetPassword(data) {
    console.log('reset', { data })
    socket.emit('authController:resetPassword', data)
  }

  function updateDetails(data) {
    socket.emit('authController:updateDetails', data)
  }

  function updatePassword(data) {
    socket.emit('authController:updatePassword', data)
  }

  function setup2Fa(data) {
    console.log('setup2Fa', { data })
    socket.emit('authController:setup2fa', data)
  }

  function verify2Fa(data) {
    console.log('verify2Fa', { data })
    socket.emit('authController:verify2fa', data)
  }

  function reset2Fa(data) {
    console.log('reset2Fa', { data })
    socket.emit('authController:reset2fa', data)
  }

  function setupOtp(data) {
    console.log('setupOtp', { data })
    socket.emit('authController:sendOTP', data)
  }

  function verifyOtp(data) {
    console.log('verifyOtp', { data })
    socket.emit('authController:verifyOTP', data)
  }

  function disableOtp(data) {
    console.log('verifyOtp', { data })
    socket.emit('authController:disableOtp', data)
  }

  function getAllSessions(data) {
    socket.emit('sessionController:getSessions', data)
  }

  function getSession(data) {
    socket.emit('sessionController:getSession', data)
  }

  function deleteSession(data) {
    socket.emit('sessionController:deleteSession', data)
  }

  function deleteAllSessions(data) {
    socket.emit('sessionController:deleteAllSessions', data)
  }

  function getNotificationSettings(data) {
    socket.emit('notificationSettingController:getNotificationSettings', data)
  }

  function createNotificationSettings(data) {
    socket.emit('notificationSettingController:createNotificationSetting', data)
  }

  function updateNotificationSettings(data) {
    socket.emit('notificationSettingController:updateNotificationSetting', data)
  }

  return {
    token,
    user,
    sessions,
    recentDevices,
    selectedNotification,
    notificationSettingsId,
    $reset,
    login,
    register,
    verifyAccount,
    resendVerifyAccount,
    logout,
    getMe,
    forgotPassword,
    resetPassword,
    updateDetails,
    updatePassword,
    setup2Fa,
    verify2Fa,
    reset2Fa,
    setupOtp,
    verifyOtp,
    disableOtp,
    getAllSessions,
    getSession,
    deleteSession,
    deleteAllSessions,
    getNotificationSettings,
    createNotificationSettings,
    updateNotificationSettings,
  }
}, {
  persistedState: {
    persist: true,
  },
})
