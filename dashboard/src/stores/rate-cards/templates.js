import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketRateCardTemplateStore = defineStore('SocketRateCardTemplate', () => {
  const templates = ref(null)
  const template = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    templates.value = null
    template.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  // Template methods
  function getAllRateCardTemplates(data) {
    console.log('Template Store: getAllRateCardTemplates called with:', data)
    socket.emit('rateCardTemplateController:getRateCardTemplates', data)
  }

  function getRateCardTemplate(data) {
    socket.emit('rateCardTemplateController:getRateCardTemplate', data)
  }

  function createRateCardTemplate(data) {
    socket.emit('rateCardTemplateController:createRateCardTemplate', data)
  }

  function updateRateCardTemplate(data) {
    socket.emit('rateCardTemplateController:updateRateCardTemplate', data)
  }

  function deleteRateCardTemplate(data) {
    socket.emit('rateCardTemplateController:deleteRateCardTemplate', data)
  }

  return {
    templates,
    template,
    pagination,
    $reset,
    getAllRateCardTemplates,
    getRateCardTemplate,
    createRateCardTemplate,
    updateRateCardTemplate,
    deleteRateCardTemplate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
