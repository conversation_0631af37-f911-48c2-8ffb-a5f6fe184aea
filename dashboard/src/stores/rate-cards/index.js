import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketRateCardStore = defineStore('SocketRateCard', () => {
  const rateCards = ref(null)
  const rateCard = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    rateCards.value = null
    rateCard.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  // Rate card methods
  function getAllRateCards(data) {
    console.log('here')
    console.log('Store: getAllRateCards called with:', data)
    socket.emit('rateCardController:getRateCards', data)
  }

  function getRateCard(data) {
    console.log('Store: getRateCard called with:', data)
    console.log('Emitting socket event: rateCardController:getRateCard')
    socket.emit('rateCardController:getRateCard', data)
  }

  function createRateCard(data) {
    console.log('Store: createRateCard called with:', data)
    socket.emit('rateCardController:createRateCard', data)
  }

  function updateRateCard(data) {
    console.log('Store: updateRateCard called with:', data)
    console.log('Emitting socket event: rateCardController:updateRateCard')
    socket.emit('rateCardController:updateRateCard', data)
  }

  function deleteRateCard(data) {
    console.log('Store: deleteRateCard called with:', data)
    socket.emit('rateCardController:deleteRateCard', data)
  }

  return {
    rateCards,
    rateCard,
    pagination,
    $reset,
    getAllRateCards,
    getRateCard,
    createRateCard,
    updateRateCard,
    deleteRateCard,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
