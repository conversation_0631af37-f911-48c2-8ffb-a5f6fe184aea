import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketTeamStore = defineStore('SocketTeam', () => {
  const teams = ref(null)
  const team = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    teams.value = null
    team.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  // Team methods
  function getTeams(data) {
    console.log('Store: getTeams called with:', data)
    socket.emit('teamController:getTeams', data)
  }

  function getTeam(data) {
    console.log('Store: getTeam called with:', data)
    socket.emit('teamController:getTeam', data)
  }

  function createTeam(data) {
    console.log('Store: createTeam called with:', data)
    socket.emit('teamController:createTeam', data)
  }

  function updateTeam(data) {
    console.log('Store: updateTeam called with:', data)
    socket.emit('teamController:updateTeam', data)
  }

  function deleteTeam(data) {
    console.log('Store: deleteTeam called with:', data)
    socket.emit('teamController:deleteTeam', data)
  }

  function addMember(data) {
    console.log('Store: addMember called with:', data)
    socket.emit('teamController:addMember', data)
  }

  function removeMember(data) {
    console.log('Store: removeMember called with:', data)
    socket.emit('teamController:removeMember', data)
  }

  function updateMemberRole(data) {
    console.log('Store: updateMemberRole called with:', data)
    socket.emit('teamController:updateMemberRole', data)
  }

  return {
    teams,
    team,
    pagination,
    $reset,
    getTeams,
    getTeam,
    createTeam,
    updateTeam,
    deleteTeam,
    addMember,
    removeMember,
    updateMemberRole,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
