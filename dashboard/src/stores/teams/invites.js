import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketTeamInviteStore = defineStore('SocketTeamInvite', () => {
  const teamInvites = ref(null)
  const teamInvite = ref(null)
  const sentInvites = ref(null)
  const receivedInvites = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    teamInvites.value = null
    teamInvite.value = null
    sentInvites.value = null
    receivedInvites.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  // Team invite methods
  function getTeamInvites(data) {
    console.log('Store: getTeamInvites called with:', data)
    socket.emit('teamInviteController:getTeamInvites', data)
  }

  function getTeamInvite(data) {
    console.log('Store: getTeamInvite called with:', data)
    socket.emit('teamInviteController:getTeamInvite', data)
  }

  function createTeamInvite(data) {
    console.log('Store: createTeamInvite called with:', data)
    socket.emit('teamInviteController:createTeamInvite', data)
  }

  function acceptTeamInvite(data) {
    console.log('Store: acceptTeamInvite called with:', data)
    socket.emit('teamInviteController:acceptTeamInvite', data)
  }

  function rejectTeamInvite(data) {
    console.log('Store: rejectTeamInvite called with:', data)
    socket.emit('teamInviteController:rejectTeamInvite', data)
  }

  function cancelTeamInvite(data) {
    console.log('Store: cancelTeamInvite called with:', data)
    socket.emit('teamInviteController:cancelTeamInvite', data)
  }

  function resendTeamInvite(data) {
    console.log('Store: resendTeamInvite called with:', data)
    socket.emit('teamInviteController:resendTeamInvite', data)
  }

  function getTeamInviteByToken(data) {
    console.log('Store: getTeamInviteByToken called with:', data)
    socket.emit('teamInviteController:getTeamInviteByToken', data)
  }

  function cleanupExpiredInvites(data) {
    console.log('Store: cleanupExpiredInvites called with:', data)
    socket.emit('teamInviteController:cleanupExpiredInvites', data)
  }

  return {
    teamInvites,
    teamInvite,
    sentInvites,
    receivedInvites,
    pagination,
    $reset,
    getTeamInvites,
    getTeamInvite,
    createTeamInvite,
    acceptTeamInvite,
    rejectTeamInvite,
    cancelTeamInvite,
    resendTeamInvite,
    getTeamInviteByToken,
    cleanupExpiredInvites,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
