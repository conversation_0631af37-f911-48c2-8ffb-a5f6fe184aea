import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketJobStore = defineStore('SocketJob', () => {
  const jobs = ref(null)
  const job = ref(null)

  function $reset() {
    jobs.value = null
    job.value = null
  }

  function getJobs(data) {
    socket.emit('jobController:getJobs', data)
  }

  function getJob(data) {
    socket.emit('jobController:getJob', data)
  }

  function createJob(data) {
    console.log({ data })
    socket.emit('jobController:createJob', data)
  }

  function updateJob(data) {
    console.log({ data })
    socket.emit('jobController:updateJob', data)
  }

  function deleteJob(data) {
    console.log({ data })
    socket.emit('jobController:deleteJob', data)
  }

  return {
    jobs,
    job,
    $reset,
    getJobs,
    getJob,
    createJob,
    update<PERSON>ob,
    deleteJob,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
