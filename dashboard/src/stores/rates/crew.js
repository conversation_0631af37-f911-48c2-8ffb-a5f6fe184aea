import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketCrewRateStore = defineStore('SocketCrewRate', () => {
  const crewRates = ref(null)
  const crewRate = ref(null)

  function $reset() {
    crewRates.value = null
    crewRate.value = null
  }

  function getCrewRates(data) {
    socket.emit('crewRateController:getCrewRates', data)
  }

  function getCrewRate(data) {
    socket.emit('crewRateController:getCrewRate', data)
  }

  function createCrewRate(data) {
    socket.emit('crewRateController:createCrewRate', data)
  }

  function updateCrewRate(data) {
    socket.emit('crewRateController:updateCrewRate', data)
  }

  function deleteCrewRate(data) {
    socket.emit('crewRateController:deleteCrewRate', data)
  }

  return {
    crewRates,
    crewRate,
    $reset,
    getCrewRates,
    getCrewRate,
    createCrewRate,
    updateCrewRate,
    deleteCrewRate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
