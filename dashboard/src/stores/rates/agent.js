import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketAgentRateStore = defineStore('SocketAgentRate', () => {
  const agentRates = ref(null)
  const agentRate = ref(null)

  function $reset() {
    agentRates.value = null
    agentRate.value = null
  }

  function getAgentRates(data) {
    socket.emit('agentRateController:getAgentRates', data)
  }

  function getAgentRate(data) {
    socket.emit('agentRateController:getAgentRate', data)
  }

  function createAgentRate(data) {
    socket.emit('agentRateController:createAgentRate', data)
  }

  function updateAgentRate(data) {
    socket.emit('agentRateController:updateAgentRate', data)
  }

  function deleteAgentRate(data) {
    socket.emit('agentRateController:deleteAgentRate', data)
  }

  return {
    agentRates,
    agentRate,
    $reset,
    getAgentRates,
    getAgentRate,
    createAgentRate,
    updateAgentRate,
    deleteAgentRate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
