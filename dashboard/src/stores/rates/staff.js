import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketStaffRateStore = defineStore('SocketStaffRate', () => {
  const staffRates = ref(null)
  const staffRate = ref(null)

  function $reset() {
    staffRates.value = null
    staffRate.value = null
  }

  function getStaffRates(data) {
    socket.emit('staffRateController:getStaffRates', data)
  }

  function getStaffRate(data) {
    socket.emit('staffRateController:getStaffRate', data)
  }

  function createStaffRate(data) {
    socket.emit('staffRateController:createStaffRate', data)
  }

  function updateStaffRate(data) {
    socket.emit('staffRateController:updateStaffRate', data)
  }

  function deleteStaffRate(data) {
    socket.emit('staffRateController:deleteStaffRate', data)
  }

  return {
    staffRates,
    staffRate,
    $reset,
    getStaffRates,
    getStaffRate,
    createStaffRate,
    updateStaffRate,
    deleteStaffRate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
