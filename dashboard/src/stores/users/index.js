import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketUserStore = defineStore('SocketUser', () => {
  const users = ref(null)
  const user = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    users.value = null
    user.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  // User methods
  function getAllUsers(data) {
    console.log('Store: getAllUsers called with:', data)
    socket.emit('userController:getUsers', data)
  }

  function getUser(data) {
    console.log('Store: getUser called with:', data)
    socket.emit('userController:getUser', data)
  }

  function createUser(data) {
    console.log('Store: createUser called with:', data)
    socket.emit('userController:createUser', data)
  }

  function updateUser(data) {
    console.log('Store: updateUser called with:', data)
    socket.emit('userController:updateUser', data)
  }

  function deleteUser(data) {
    console.log('Store: deleteUser called with:', data)
    socket.emit('userController:deleteUser', data)
  }

  return {
    users,
    user,
    pagination,
    $reset,
    getAllUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
