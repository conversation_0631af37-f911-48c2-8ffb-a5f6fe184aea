import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketProductionStore = defineStore('SocketProduction', () => {
  const productions = ref(null)
  const production = ref(null)

  function $reset() {
    productions.value = null
    production.value = null
  }

  function getProductions(data) {
    socket.emit('productionController:getProductions',  data)
  }

  function getProduction(data) {
    socket.emit('productionController:getProduction', data)
  }

  function createProduction(data) {
    socket.emit('productionController:createProduction', data)
  }

  function updateProduction(data) {
    socket.emit('productionController:updateProduction', data)
  }

  function deleteProduction(data) {
    socket.emit('productionController:deleteProduction', data)
  }

  // socketStore.socketObject.emit()
  return {
    productions,
    production,
    $reset,
    getProductions,
    getProduction,
    createProduction,
    updateProduction,
    deleteProduction,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
