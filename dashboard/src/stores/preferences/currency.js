import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketCurrencyStore = defineStore('SocketCurrency', () => {
  const currencies = ref(null)
  const currency = ref(null)

  function $reset() {
    currencies.value = null
    currency.value = null
  }

  function getCurrencies(data) {
    socket.emit('currencyController:getCurrencies', data)
  }

  function getCurrency(data) {
    socket.emit('currencyController:getCurrency', data)
  }

  function createCurrency(data) {
    socket.emit('currencyController:createCurrency', data)
  }

  function updateCurrency(data) {
    socket.emit('currencyController:updateCurrency', data)
  }

  function deleteCurrency(data) {
    socket.emit('currencyController:deleteCurrency', data)
  }

  return {
    currencies,
    currency,
    $reset,
    getCurrencies,
    getCurrency,
    createCurrency,
    updateCurrency,
    deleteCurrency,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
