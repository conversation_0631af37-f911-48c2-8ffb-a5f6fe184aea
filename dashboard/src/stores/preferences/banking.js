import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketBankingStore = defineStore('SocketBanking', () => {
  const bankAccounts = ref(null)
  const bankAccount = ref(null)

  function $reset() {
    bankAccounts.value = null
    bankAccount.value = null
  }

  function getBankingAccounts(data) {
    socket.emit('bankAccountController:getBankAccounts', data)
  }

  function getBankingAccount(data) {
    socket.emit('bankAccountController:getBankAccount', data)
  }

  function createBankingAccount(data) {
    socket.emit('bankAccountController:createBankAccount', data)
  }

  function updateBankingAccount(data) {
    socket.emit('bankAccountController:updateBankAccount', data)
  }

  function deleteBankingAccount(data) {
    socket.emit('bankAccountController:deleteBankAccount', data)
  }

  return {
    bankAccounts,
    bankAccount,
    $reset,
    getBankingAccounts,
    getBankingAccount,
    createBankingAccount,
    updateBankingAccount,
    deleteBankingAccount,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
