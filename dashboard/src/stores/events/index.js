import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketEventStore = defineStore('SocketEvent', () => {
  const events = ref(null)
  const event = ref(null)

  function $reset() {
    events.value = null
    event.value = null
  }

  function getAllEvents(data) {
    socket.emit('eventController:getAllEvents', data)
  }

  function getEventsByDateRange(data) {
    socket.emit('eventController:getEventsByDateRange', data)
  }

  function getEvent(data) {
    socket.emit('eventController:getEvent', data)
  }

  function createEvent(data) {
    socket.emit('eventController:createEvent', data)
  }

  function updateEvent(data) {
    socket.emit('eventController:updateEvent', data)
  }

  function deleteEvent(data) {
    socket.emit('eventController:deleteEvent', data)
  }

  return {
    events,
    event,
    $reset,
    getAllEvents,
    getEventsByDateRange,
    getEvent,
    createEvent,
    updateEvent,
    deleteEvent,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
