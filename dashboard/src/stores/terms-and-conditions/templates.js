import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketTermConditionTemplateStore = defineStore('SocketTermConditionTemplate', () => {
  const templates = ref(null)
  const template = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    templates.value = null
    template.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  // Template methods
  function getAllTermConditionTemplates(data) {
    socket.emit('termConditionTemplateController:getAllTermConditionTemplates', data)
  }

  function getTermConditionTemplate(data) {
    socket.emit('termConditionTemplateController:getTermConditionTemplate', data)
  }

  function createTermConditionTemplate(data) {
    socket.emit('termConditionTemplateController:createTermConditionTemplate', data)
  }

  function updateTermConditionTemplate(data) {
    socket.emit('termConditionTemplateController:updateTermConditionTemplate', data)
  }

  function deleteTermConditionTemplate(data) {
    socket.emit('termConditionTemplateController:deleteTermConditionTemplate', data)
  }

  return {
    templates,
    template,
    pagination,
    $reset,
    getAllTermConditionTemplates,
    getTermConditionTemplate,
    createTermConditionTemplate,
    updateTermConditionTemplate,
    deleteTermConditionTemplate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
