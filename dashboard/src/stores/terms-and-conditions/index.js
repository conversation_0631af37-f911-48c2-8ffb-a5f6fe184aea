import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketTermConditionStore = defineStore('SocketTermCondition', () => {
  const termConditions = ref(null)
  const termCondition = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    termConditions.value = null
    termCondition.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  // Term condition methods
  function getAllTermConditions(data) {
    socket.emit('termConditionController:getAllTermConditions', data)
  }

  function getTermCondition(data) {
    socket.emit('termConditionController:getTermCondition', data)
  }

  function createTermCondition(data) {
    socket.emit('termConditionController:createTermCondition', data)
  }

  function updateTermCondition(data) {
    socket.emit('termConditionController:updateTermCondition', data)
  }

  function deleteTermCondition(data) {
    socket.emit('termConditionController:deleteTermCondition', data)
  }

  return {
    termConditions,
    termCondition,
    pagination,
    $reset,
    getAllTermConditions,
    getTermCondition,
    createTermCondition,
    updateTermCondition,
    deleteTermCondition,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
