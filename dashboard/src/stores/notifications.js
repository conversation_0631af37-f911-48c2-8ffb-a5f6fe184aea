import { socket } from '@socket/socket'
import { defineStore } from 'pinia'

export const useNotificationsSocketStore = defineStore('NotificationSocket', () => {
  const appNotifications = ref(null)
  const appNotification = ref(null)
  const notifications = ref(null)
  const notification = ref(null)

  function $reset() {
    appNotifications.value = null
    appNotification.value = null
    notifications.value = null
    notification.value = null
  }

  function getGlobalNotifications(data) {
    socket.emit('appNotification:getGlobalNotifications', data)
  }

  function sendGlobalNotification(data) {
    socket.emit('appNotification:sendGlobalNotification', data)
  }

  function sendSingleUserNotification(data) {
    socket.emit('appNotification:sendSingleUserNotification', data)
  }

  function markAsReadNotification(data) {
    socket.emit('appNotification:markAsRead', data)
  }

  function markAsUnreadNotification(data) {
    socket.emit('appNotification:markAsUnread', data)
  }

  function getNotifications(data) {
    socket.emit('notificationController:getNotifications', data)
  }

  function getNotification(data) {
    socket.emit('notificationController:getNotification', data)
  }

  function createNotification(data) {
    socket.emit('notificationController:createNotification', data)
  }

  function updateNotification(data) {
    socket.emit('notificationController:updateNotification', data)
  }

  function deleteNotification(data) {
    socket.emit('notificationController:deleteNotification', data)
  }

  function markAllNotificationsAsRead(data) {
    socket.emit('notificationController:markAllAsRead', data)
  }

  function markNotificationAsRead(data) {
    socket.emit('notificationController:markAsReadNotification', data)
  }

  function markAllNotificationsAsUnread(data) {
    socket.emit('notificationController:markAllAsUnread', data)
  }

  function markNotificationAsUnread(data) {
    socket.emit('notificationController:markAsUnreadNotification', data)
  }

  return {
    appNotifications,
    appNotification,
    notifications,
    notification,
    $reset,
    getGlobalNotifications,
    sendGlobalNotification,
    sendSingleUserNotification,
    markAsReadNotification,
    markAsUnreadNotification,
    getNotifications,
    getNotification,
    createNotification,
    updateNotification,
    deleteNotification,
    markAllNotificationsAsRead,
    markNotificationAsRead,
    markAllNotificationsAsUnread,
    markNotificationAsUnread,
  }
})
