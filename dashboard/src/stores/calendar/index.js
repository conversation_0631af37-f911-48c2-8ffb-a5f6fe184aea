import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketCalendarStore = defineStore('SocketCalendar', () => {
  const calendars = ref(null)
  const calendar = ref(null)

  function $reset() {
    calendars.value = null
    calendar.value = null
  }

  function getAllCalendars(data) {
    socket.emit('calendarController:getAllCalendars', data)
  }

  function getCalendar(data) {
    socket.emit('calendarController:getCalendar', data)
  }

  function createCalendar(data) {
    socket.emit('calendarController:createCalendar', data)
  }

  function updateCalendar(data) {
    socket.emit('calendarController:updateCalendar', data)
  }

  function deleteCalendar(data) {
    socket.emit('calendarController:deleteCalendar', data)
  }

  return {
    calendars,
    calendar,
    $reset,
    getAllCalendars,
    getCalendar,
    createCalendar,
    updateCalendar,
    deleteCalendar,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
