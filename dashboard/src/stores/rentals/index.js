import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketRentalStore = defineStore('SocketRental', () => {
  const rentals = ref(null)
  const rental = ref(null)

  function $reset() {
    rentals.value = null
    rental.value = null
  }

  function getRentals(data) {
    socket.emit('rentalController:getRentals', data)
  }

  function getRental(data) {
    socket.emit('rentalController:getRental', data)
  }

  function createRental(data) {
    console.log({ data })
    socket.emit('rentalController:createRental', data)
  }

  function updateRental(data) {
    console.log({ data })
    socket.emit('rentalController:updateRental', data)
  }

  function deleteRental(data) {
    console.log({ data })
    socket.emit('rentalController:deleteRental', data)
  }

  return {
    rentals,
    rental,
    $reset,
    getRentals,
    getRental,
    createRental,
    updateRental,
    deleteRental,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
