import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketRentalBookingStore = defineStore('SocketRentalBooking', () => {
  const bookings = ref(null)
  const booking = ref(null)

  function $reset() {
    bookings.value = null
    booking.value = null
  }

  function getBookings(data) {
    socket.emit('rentalBookingController:getBookings', data)
  }

  function getBooking(data) {
    socket.emit('rentalBookingController:getBooking', data)
  }

  function createBooking(data) {
    console.log({ data })
    socket.emit('rentalBookingController:createBooking', data)
  }

  function updateBooking(data) {
    console.log({ data })
    socket.emit('rentalBookingController:updateBooking', data)
  }

  function deleteBooking(data) {
    console.log({ data })
    socket.emit('rentalBookingController:deleteBooking', data)
  }

  function changeBookingStatus(data) {
    console.log({ data })
    socket.emit('rentalBookingController:changeBookingStatus', data)
  }

  function checkOutBooking(data) {
    console.log({ data })
    socket.emit('rentalBookingController:checkOutBooking', data)
  }

  function checkInBooking(data) {
    console.log({ data })
    socket.emit('rentalBookingController:checkInBooking', data)
  }

  return {
    bookings,
    booking,
    $reset,
    getBookings,
    getBooking,
    createBooking,
    updateBooking,
    deleteBooking,
    changeBookingStatus,
    checkOutBooking,
    checkInBooking,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
