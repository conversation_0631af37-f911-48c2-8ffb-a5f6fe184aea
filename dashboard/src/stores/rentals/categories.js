import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketRentalCategoryStore = defineStore('SocketRentalCategory', () => {
  const categories = ref(null)
  const category = ref(null)

  function $reset() {
    categories.value = null
    category.value = null
  }

  function getCategories(data) {
    socket.emit('rentalCategoryController:getCategories', data)
  }

  function getCategory(data) {
    socket.emit('rentalCategoryController:getCategory', data)
  }

  function createCategory(data) {
    console.log({ data })
    socket.emit('rentalCategoryController:createCategory', data)
  }

  function updateCategory(data) {
    console.log({ data })
    socket.emit('rentalCategoryController:updateCategory', data)
  }

  function deleteCategory(data) {
    console.log({ data })
    socket.emit('rentalCategoryController:deleteCategory', data)
  }

  return {
    categories,
    category,
    $reset,
    getCategories,
    getCategory,
    createCategory,
    updateCategory,
    deleteCategory,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
