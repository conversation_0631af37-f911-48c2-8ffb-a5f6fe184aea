import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketIdentityStore = defineStore('SocketIdentity', () => {
  const identities = ref(null)
  const identity = ref(null)

  function $reset() {
    identities.value = null
    identity.value = null
  }

  function getIdentities(data) {
    socket.emit('identityController:getIdentities',  data)
  }

  function getIdentity(data) {
    socket.emit('identityController:getIdentity', data)
  }

  function createIdentityFromOnboarding(data) {
    socket.emit('identityController:createOnboardingIdentity', data)
  }

  function createIdentity(data) {
    socket.emit('identityController:createIdentity', data)
  }

  function updateIdentity(data) {
    socket.emit('identityController:updateIdentity', data)
  }

  function deleteIdentity(data) {
    socket.emit('identityController:deleteIdentity', data)
  }

  // socketStore.socketObject.emit()
  return {
    identities,
    identity,
    $reset,
    getIdentities,
    getIdentity,
    createIdentity,
    createIdentityFromOnboarding,
    updateIdentity,
    deleteIdentity,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
