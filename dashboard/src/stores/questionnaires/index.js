import { defineS<PERSON> } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketQuestionnaireStore = defineStore('SocketQuestionnaire', () => {
  const questionnaires = ref(null)
  const questionnaire = ref(null)
  const questions = ref(null)
  const answers = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    questionnaires.value = null
    questionnaire.value = null
    questions.value = null
    answers.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  function getQuestionnaires(data) {
    socket.emit('questionnaireController:getAllQuestionnaires', data)
  }

  function getQuestionnaire(data) {
    socket.emit('questionnaireController:getQuestionnaire', data)
  }

  function createQuestionnaire(data) {
    socket.emit('questionnaireController:createQuestionnaire', data)
  }

  function updateQuestionnaire(data) {
    socket.emit('questionnaireController:updateQuestionnaire', data)
  }

  function deleteQuestionnaire(data) {
    socket.emit('questionnaireController:deleteQuestionnaire', data)
  }

  function getQuestions(data) {
    socket.emit('questionnaireQuestionController:getQuestionQuestionnaire', data)
  }

  function createQuestion(data) {
    socket.emit('questionnaireQuestionController:createQuestionnaireQuestion', data)
  }

  function updateQuestion(data) {
    socket.emit('questionnaireQuestionController:updateQuestionnaireQuestion', data)
  }

  function deleteQuestion(data) {
    socket.emit('questionnaireQuestionController:deleteQuestionnaireQuestion', data)
  }

  function getAnswers(data) {
    socket.emit('questionnaireAnswerController:getAnswersByQuestionnaire', data)
  }

  function submitAnswers(data) {
    socket.emit('questionnaireAnswerController:submitAnswers', data)
  }

  return {
    questionnaires,
    questionnaire,
    questions,
    answers,
    pagination,
    $reset,
    getQuestionnaires,
    getQuestionnaire,
    createQuestionnaire,
    updateQuestionnaire,
    deleteQuestionnaire,
    getQuestions,
    createQuestion,
    updateQuestion,
    deleteQuestion,
    getAnswers,
    submitAnswers,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
