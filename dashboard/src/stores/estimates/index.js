import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketEstimateStore = defineStore('SocketEstimate', () => {
  const estimates = ref(null)
  const estimate = ref(null)
  const estimateTemplates = ref(null)
  const estimateTemplate = ref(null)

  function $reset() {
    estimates.value = null
    estimate.value = null
    estimateTemplates.value = null
    estimateTemplate.value = null
  }

  function getEstimates(data) {
    socket.emit('estimateController:getEstimates', data)
  }

  function getEstimate(data) {
    socket.emit('estimateController:getEstimate', data)
  }

  function createEstimate(data) {
    console.log({ data })
    socket.emit('estimateController:createEstimate', data)
  }

  function updateEstimate(data) {
    console.log({ data })
    socket.emit('estimateController:updateEstimate', data)
  }

  function deleteEstimate(data) {
    console.log({ data })
    socket.emit('estimateController:deleteEstimate', data)
  }

  function getTemplates(data) {
    console.log({ data })
    socket.emit('estimateTemplateController:getEstimateTemplates', data)
  }

  function getTemplate(data) {
    console.log({ data })
    socket.emit('estimateTemplateController:getEstimateTemplate', data)
  }

  return {
    estimates,
    estimate,
    estimateTemplates,
    estimateTemplate,
    $reset,
    getEstimates,
    getEstimate,
    createEstimate,
    updateEstimate,
    deleteEstimate,
    getTemplates,
    getTemplate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
