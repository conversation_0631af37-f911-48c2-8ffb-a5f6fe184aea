import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketMoodBoardStore = defineStore('SocketMoodBoard', () => {
  const moodBoards = ref(null)
  const moodBoard = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 12,
    total: 0,
  })

  function $reset() {
    moodBoards.value = null
    moodBoard.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 12,
      total: 0,
    }
  }

  function getAllMoodBoards(data) {
    socket.emit('moodBoardController:getAllMoodBoards', data)
  }

  function getMoodBoard(data) {
    socket.emit('moodBoardController:getMoodBoard', data)
  }

  function createMoodBoard(data) {
    socket.emit('moodBoardController:createMoodBoard', data)
  }

  function updateMoodBoard(data) {
    socket.emit('moodBoardController:updateMoodBoard', data)
  }

  function deleteMoodBoard(data) {
    socket.emit('moodBoardController:deleteMoodBoard', data)
  }

  function duplicateMoodBoard(data) {
    socket.emit('moodBoardController:duplicateMoodBoard', data)
  }

  function addMoodBoardItem(data) {
    socket.emit('moodBoardController:addMoodBoardItem', data)
  }

  function updateMoodBoardItem(data) {
    socket.emit('moodBoardController:updateMoodBoardItem', data)
  }

  function deleteMoodBoardItem(data) {
    socket.emit('moodBoardController:deleteMoodBoardItem', data)
  }

  return {
    moodBoards,
    moodBoard,
    pagination,
    $reset,
    getAllMoodBoards,
    getMoodBoard,
    createMoodBoard,
    updateMoodBoard,
    deleteMoodBoard,
    duplicateMoodBoard,
    addMoodBoardItem,
    updateMoodBoardItem,
    deleteMoodBoardItem,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
