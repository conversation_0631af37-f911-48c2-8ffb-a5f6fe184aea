import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketTodoChecklistStore = defineStore('SocketTodoChecklist', () => {
  const todoChecklists = ref(null)
  const todoChecklist = ref(null)

  function $reset() {
    todoChecklists.value = null
    todoChecklist.value = null
  }

  // Todo Checklist CRUD operations
  function getAllTodoChecklists(data) {
    socket.emit('todoChecklistController:getAllTodoChecklists', data)
  }

  function getTodoChecklist(data) {
    socket.emit('todoChecklistController:getTodoChecklist', data)
  }

  function createTodoChecklist(data) {
    socket.emit('todoChecklistController:createTodoChecklist', data)
  }

  function updateTodoChecklist(data) {
    socket.emit('todoChecklistController:updateTodoChecklist', data)
  }

  function deleteTodoChecklist(data) {
    socket.emit('todoChecklistController:deleteTodoChecklist', data)
  }

  // Todo Item operations
  function addTodoItem(data) {
    socket.emit('todoChecklistController:addTodoItem', data)
  }

  function updateTodoItem(data) {
    socket.emit('todoChecklistController:updateTodoItem', data)
  }

  function deleteTodoItem(data) {
    socket.emit('todoChecklistController:deleteTodoItem', data)
  }

  function toggleTodoItem(data) {
    socket.emit('todoChecklistController:toggleTodoItem', data)
  }

  function reorderTodoItems(data) {
    socket.emit('todoChecklistController:reorderTodoItems', data)
  }

  return {
    todoChecklists,
    todoChecklist,
    $reset,
    getAllTodoChecklists,
    getTodoChecklist,
    createTodoChecklist,
    updateTodoChecklist,
    deleteTodoChecklist,
    addTodoItem,
    updateTodoItem,
    deleteTodoItem,
    toggleTodoItem,
    reorderTodoItems,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
