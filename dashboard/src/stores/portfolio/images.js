import { defineStore } from 'pinia'
import { ref } from 'vue'
import { socket } from '@socket/socket'

export const usePortfolioImageStore = defineStore('portfolioImages', () => {
  // State
  const images = ref([])
  const uploading = ref(false)
  const uploadProgress = ref(0)
  const uploadingFiles = ref([])

  /**
   * Upload multiple images for portfolio components
   * @param {Object} data - Upload data
   * @param {string} data.user - User ID
   * @param {string} data.portfolioId - Portfolio ID
   * @param {string} data.componentType - Component type (masonry, grid, etc.)
   * @param {Array} data.images - Array of image objects with base64 data
   */
  function uploadImages(data) {
    console.log('📤 Portfolio Image Store: Uploading images', {
      user: data.user,
      portfolioId: data.portfolioId,
      componentType: data.componentType,
      imageCount: data.images?.length || 0,
    })

    uploading.value = true
    uploadProgress.value = 0
    uploadingFiles.value = data.images.map(img => img.filename)

    socket.emit('portfolioImageController:uploadImages', data)
  }

  /**
   * Delete a portfolio image
   * @param {Object} data - Delete data
   * @param {string} data.user - User ID
   * @param {string} data.portfolioId - Portfolio ID
   * @param {string} data.filename - Filename to delete
   */
  function deleteImage(data) {
    console.log('🗑️ Portfolio Image Store: Deleting image', data)
    socket.emit('portfolioImageController:deleteImage', data)
  }

  /**
   * Get portfolio images
   * @param {Object} data - Request data
   * @param {string} data.user - User ID
   * @param {string} data.portfolioId - Portfolio ID
   */
  function getImages(data) {
    console.log('📋 Portfolio Image Store: Getting images', data)
    socket.emit('portfolioImageController:getImages', data)
  }

  /**
   * Convert file to upload format
   * @param {File} file - File object
   * @returns {Promise<Object>} Upload-ready image object
   */
  async function prepareImageForUpload(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = () => {
        resolve({
          filename: file.name,
          base64: reader.result,
          size: file.size,
          type: file.type,
        })
      }
      
      reader.onerror = () => {
        reject(new Error(`Failed to read file: ${file.name}`))
      }
      
      reader.readAsDataURL(file)
    })
  }

  /**
   * Prepare multiple files for upload
   * @param {Array<File>} files - Array of file objects
   * @returns {Promise<Array>} Array of upload-ready image objects
   */
  async function prepareMultipleImagesForUpload(files) {
    const promises = files.map(file => prepareImageForUpload(file))
    
    return Promise.all(promises)
  }

  /**
   * Upload files with automatic preparation
   * @param {Object} data - Upload data
   * @param {string} data.user - User ID
   * @param {string} data.portfolioId - Portfolio ID
   * @param {string} data.componentType - Component type
   * @param {Array<File>} data.files - Array of File objects
   */
  async function uploadFiles(data) {
    try {
      console.log('🔄 Portfolio Image Store: Preparing files for upload', {
        fileCount: data.files?.length || 0,
      })

      // Convert files to base64
      const preparedImages = await prepareMultipleImagesForUpload(data.files)
      
      // Upload prepared images
      uploadImages({
        user: data.user,
        portfolioId: data.portfolioId,
        componentType: data.componentType,
        images: preparedImages,
      })
    } catch (error) {
      console.error('❌ Portfolio Image Store: Error preparing files', error)
      uploading.value = false
      throw error
    }
  }

  /**
   * Reset upload state
   */
  function resetUploadState() {
    uploading.value = false
    uploadProgress.value = 0
    uploadingFiles.value = []
  }

  /**
   * Update upload progress
   * @param {number} current - Current file number
   * @param {number} total - Total files
   */
  function updateProgress(current, total) {
    uploadProgress.value = Math.round((current / total) * 100)
  }

  return {
    // State
    images,
    uploading,
    uploadProgress,
    uploadingFiles,

    // Actions
    uploadImages,
    uploadFiles,
    deleteImage,
    getImages,
    prepareImageForUpload,
    prepareMultipleImagesForUpload,
    resetUploadState,
    updateProgress,
  }
})
