import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketProductionInvoiceStore = defineStore('SocketProductionInvoice', () => {
  const productionInvoices = ref(null)
  const productionInvoice = ref(null)

  function $reset() {
    productionInvoices.value = null
    productionInvoice.value = null
  }

  function getProductionInvoices(data) {
    socket.emit('invoiceProductionController:getProductionInvoices', data)
  }

  function getProductionInvoice(data) {
    socket.emit('invoiceProductionController:getProductionInvoice', data)
  }

  function createProductionInvoice(data) {
    console.log({ data })
    socket.emit('invoiceProductionController:createProductionInvoice', data)
  }

  function updateProductionInvoice(data) {
    console.log({ data })
    socket.emit('invoiceProductionController:updateProductionInvoice', data)
  }

  function deleteProductionInvoice(data) {
    console.log({ data })
    socket.emit('invoiceProductionController:deleteProductionInvoice', data)
  }

  return {
    productionInvoices,
    productionInvoice,
    $reset,
    getProductionInvoices,
    getProductionInvoice,
    createProductionInvoice,
    updateProductionInvoice,
    deleteProductionInvoice,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
