import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketClientStore = defineStore('SocketClient', () => {
  const clients = ref(null)
  const client = ref(null)

  function $reset() {
    clients.value = null
    client.value = null
  }

  function getClients(data) {
    socket.emit('clientController:getClients', data)
  }

  function getClient(data) {
    socket.emit('clientController:getClient', data)
  }

  function createClient(data) {
    socket.emit('clientController:createClient', data)
  }

  function updateClient(data) {
    socket.emit('clientController:updateClient', data)
  }

  function deleteClient(data) {
    socket.emit('clientController:deleteClient', data)
  }

  return {
    clients,
    client,
    $reset,
    getClients,
    getClient,
    createClient,
    updateClient,
    deleteClient,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
