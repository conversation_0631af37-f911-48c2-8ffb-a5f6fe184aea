import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketInvoiceStore = defineStore('SocketInvoice', () => {
  const invoices = ref(null)
  const invoice = ref(null)
  const invoiceTemplates = ref(null)
  const invoiceTemplate = ref(null)

  function $reset() {
    invoices.value = null
    invoice.value = null
    invoiceTemplates.value = null
    invoiceTemplate.value = null
  }

  function getInvoices(data) {
    socket.emit('invoiceController:getInvoices', data)
  }

  function getInvoice(data) {
    socket.emit('invoiceController:getInvoice', data)
  }

  function createInvoice(data) {
    console.log({ data })
    socket.emit('invoiceController:createInvoice', data)
  }

  function updateInvoice(data) {
    console.log({ data })
    socket.emit('invoiceController:updateInvoice', data)
  }

  function deleteInvoice(data) {
    console.log({ data })
    socket.emit('invoiceController:deleteInvoice', data)
  }

  function getTemplates(data) {
    console.log({ data })
    socket.emit('invoiceTemplateController:getInvoiceTemplates', data)
  }

  function getTemplate(data) {
    console.log({ data })
    socket.emit('invoiceTemplateController:getInvoiceTemplate', data)
  }

  function sendInvoice(data) {
    console.log({ data })
    socket.emit('invoiceController:sendInvoice', data)
  }

  return {
    invoices,
    invoice,
    invoiceTemplates,
    invoiceTemplate,
    $reset,
    getInvoices,
    getInvoice,
    createInvoice,
    updateInvoice,
    deleteInvoice,
    getTemplates,
    getTemplate,
    sendInvoice,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
