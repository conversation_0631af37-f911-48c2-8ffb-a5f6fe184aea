import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketQuoteStore = defineStore('SocketQuote', () => {
  const quotes = ref(null)
  const quote = ref(null)
  const quoteTemplates = ref(null)
  const quoteTemplate = ref(null)

  function $reset() {
    quotes.value = null
    quote.value = null
    quoteTemplates.value = null
    quoteTemplate.value = null
  }

  function getQuotes(data) {
    socket.emit('quoteController:getQuotes', data)
  }

  function getQuote(data) {
    socket.emit('quoteController:getQuote', data)
  }

  function createQuote(data) {
    console.log({ data })
    socket.emit('quoteController:createQuote', data)
  }

  function updateQuote(data) {
    console.log({ data })
    socket.emit('quoteController:updateQuote', data)
  }

  function deleteQuote(data) {
    console.log({ data })
    socket.emit('quoteController:deleteQuote', data)
  }

  function getTemplates(data) {
    console.log({ data })
    socket.emit('quoteTemplateController:getQuoteTemplates', data)
  }

  function getTemplate(data) {
    console.log({ data })
    socket.emit('quoteTemplateController:getQuoteTemplate', data)
  }

  function sendQuote(data) {
    console.log({ data })
    socket.emit('quoteController:sendQuote', data)
  }

  return {
    quotes,
    quote,
    quoteTemplates,
    quoteTemplate,
    $reset,
    getQuotes,
    getQuote,
    createQuote,
    updateQuote,
    deleteQuote,
    getTemplates,
    getTemplate,
    sendQuote,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
