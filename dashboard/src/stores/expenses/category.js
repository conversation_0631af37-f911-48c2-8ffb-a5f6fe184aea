import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketExpenseCategoryStore = defineStore('SocketExpenseCategory', () => {
  const expenseCategories = ref(null)
  const expenseCategory = ref(null)

  function $reset() {
    expenseCategories.value = null
    expenseCategory.value = null
  }

  function getExpenseCategories(data) {
    socket.emit('expenseCategoryController:getExpenseCategories', data)
  }

  function getExpenseCategory(data) {
    socket.emit('expenseCategoryController:getExpenseCategory', data)
  }

  function createExpenseCategory(data) {
    socket.emit('expenseCategoryController:createExpenseCategory', data)
  }

  function updateExpenseCategory(data) {
    socket.emit('expenseCategoryController:updateExpenseCategory', data)
  }

  function deleteExpenseCategory(data) {
    socket.emit('expenseCategoryController:deleteExpenseCategory', data)
  }

  return {
    expenseCategories,
    expenseCategory,
    $reset,
    getExpenseCategories,
    getExpenseCategory,
    createExpenseCategory,
    updateExpenseCategory,
    deleteExpenseCategory,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
