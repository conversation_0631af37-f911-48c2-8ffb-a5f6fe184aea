import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketExpenseStore = defineStore('SocketExpense', () => {
  const expenses = ref(null)
  const expense = ref(null)

  function $reset() {
    expenses.value = null
    expense.value = null
  }

  function getExpenses(data) {
    socket.emit('expenseController:getExpenses', data)
  }

  function getExpense(data) {
    socket.emit('expenseController:getExpense', data)
  }

  function createExpense(data) {
    socket.emit('expenseController:createExpense', data)
  }

  function updateExpense(data) {
    socket.emit('expenseController:updateExpense', data)
  }

  function deleteExpense(data) {
    socket.emit('expenseController:deleteExpense', data)
  }

  return {
    expenses,
    expense,
    $reset,
    getExpenses,
    getExpense,
    createExpense,
    updateExpense,
    deleteExpense,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
