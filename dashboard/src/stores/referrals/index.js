import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketReferralStore = defineStore('SocketReferral', () => {
  const referrals = ref(null)
  const referralStats = ref(null)
  const referralLink = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    referrals.value = null
    referralStats.value = null
    referralLink.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  function getAllReferrals(data) {
    socket.emit('referralController:getAllReferrals', data)
  }

  function getReferralStats(data) {
    socket.emit('referralController:getReferralStats', data)
  }

  function createReferralInvite(data) {
    socket.emit('referralController:createReferralInvite', data)
  }

  function getReferralLink(data) {
    socket.emit('referralController:getReferralLink', data)
  }

  function updateReferralStatus(data) {
    socket.emit('referralController:updateReferralStatus', data)
  }

  function deleteReferral(data) {
    socket.emit('referralController:deleteReferral', data)
  }

  // Legacy referral methods (keeping for compatibility)
  function createReferralLink(data) {
    socket.emit('referralController:createReferralLink', data)
  }

  function createReferralFee(data) {
    socket.emit('referralController:createReferralFee', data)
  }

  function createReferralDiscount(data) {
    socket.emit('referralController:createReferralDiscount', data)
  }

  return {
    referrals,
    referralStats,
    referralLink,
    pagination,
    $reset,
    getAllReferrals,
    getReferralStats,
    createReferralInvite,
    getReferralLink,
    updateReferralStatus,
    deleteReferral,
    createReferralLink,
    createReferralFee,
    createReferralDiscount,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
