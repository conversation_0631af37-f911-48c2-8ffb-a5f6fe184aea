import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketBookingStore = defineStore('SocketBooking', () => {
  const bookings = ref(null)
  const booking = ref(null)

  function $reset() {
    bookings.value = null
    booking.value = null
  }

  function getAllBookings(data) {
    socket.emit('bookingController:getAllBookings', data)
  }

  function getBookingsByDateRange(data) {
    socket.emit('bookingController:getBookingsByDateRange', data)
  }

  function getBooking(data) {
    socket.emit('bookingController:getBooking', data)
  }

  function createBooking(data) {
    socket.emit('bookingController:createBooking', data)
  }

  function updateBooking(data) {
    socket.emit('bookingController:updateBooking', data)
  }

  function deleteBooking(data) {
    socket.emit('bookingController:deleteBooking', data)
  }

  return {
    bookings,
    booking,
    $reset,
    getAllBookings,
    getBookingsByDateRange,
    getBooking,
    createBooking,
    updateBooking,
    deleteBooking,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
