import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useAdminIdentityManagementStore = defineStore('AdminIdentityManagement', () => {
  const analytics = ref(null)
  const usageData = ref(null)
  const approachingLimits = ref(null)
  const bulkOperationResults = ref(null)
  const loading = ref(false)

  function $reset() {
    analytics.value = null
    usageData.value = null
    approachingLimits.value = null
    bulkOperationResults.value = null
    loading.value = false
  }

  // Analytics methods
  function getIdentityUsageAnalytics(data) {
    console.log('Store: getIdentityUsageAnalytics called with:', data)
    loading.value = true
    socket.emit('userController:getIdentityUsageAnalytics', data)
  }

  function getUsersApproachingLimits(data) {
    console.log('Store: getUsersApproachingLimits called with:', data)
    loading.value = true
    socket.emit('userController:getUsersApproachingLimits', data)
  }

  function getUserIdentityUsage(data) {
    console.log('Store: getUserIdentityUsage called with:', data)
    socket.emit('userController:getUserIdentityUsage', data)
  }

  // Individual user management
  function updateUserMaxIdentities(data) {
    console.log('Store: updateUserMaxIdentities called with:', data)
    socket.emit('userController:updateUserMaxIdentities', data)
  }

  function removeMaxIdentitiesOverride(data) {
    console.log('Store: removeMaxIdentitiesOverride called with:', data)
    socket.emit('userController:removeMaxIdentitiesOverride', data)
  }

  // Bulk operations
  function bulkUpdateMaxIdentities(data) {
    console.log('Store: bulkUpdateMaxIdentities called with:', data)
    loading.value = true
    socket.emit('userController:bulkUpdateMaxIdentities', data)
  }

  function bulkRemoveMaxIdentitiesOverride(data) {
    console.log('Store: bulkRemoveMaxIdentitiesOverride called with:', data)
    loading.value = true
    socket.emit('userController:bulkRemoveMaxIdentitiesOverride', data)
  }

  // Subscription management
  function updateUserSubscription(data) {
    console.log('Store: updateUserSubscription called with:', data)
    socket.emit('userSubscriptionController:updateUserSubscription', data)
  }

  return {
    analytics,
    usageData,
    approachingLimits,
    bulkOperationResults,
    loading,
    $reset,
    getIdentityUsageAnalytics,
    getUsersApproachingLimits,
    getUserIdentityUsage,
    updateUserMaxIdentities,
    removeMaxIdentitiesOverride,
    bulkUpdateMaxIdentities,
    bulkRemoveMaxIdentitiesOverride,
    updateUserSubscription,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
