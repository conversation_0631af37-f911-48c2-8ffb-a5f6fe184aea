import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useAdminChatManagementStore = defineStore('AdminChatManagement', () => {
  const allChats = ref([])
  const chatAnalytics = ref(null)
  const messageAnalytics = ref(null)
  const selectedUserChats = ref([])
  const loading = ref(false)

  const pagination = ref({
    page: 1,
    itemsPerPage: 20,
    total: 0,
  })

  function $reset() {
    allChats.value = []
    chatAnalytics.value = null
    messageAnalytics.value = null
    selectedUserChats.value = []
    loading.value = false
    pagination.value = {
      page: 1,
      itemsPerPage: 20,
      total: 0,
    }
  }

  // Admin chat management methods
  function getAllChatsAdmin(data) {
    console.log('Store: getAllChatsAdmin called with:', data)
    loading.value = true
    socket.emit('chatController:getAllChatsAdmin', data)
  }

  function getChatAnalytics(data) {
    console.log('Store: getChatAnalytics called with:', data)
    loading.value = true
    socket.emit('chatController:getChatAnalytics', data)
  }

  function getMessageAnalytics(data) {
    console.log('Store: getMessageAnalytics called with:', data)
    loading.value = true
    socket.emit('messageController:getMessageAnalytics', data)
  }

  function adminDeleteMessage(data) {
    console.log('Store: adminDeleteMessage called with:', data)
    socket.emit('messageController:adminDeleteMessage', data)
  }

  // Get user's chats (admin access)
  function getUserChats(data) {
    console.log('Store: getUserChats called with:', data)
    loading.value = true
    socket.emit('chatController:getChats', data)
  }

  function getUserChat(data) {
    console.log('Store: getUserChat called with:', data)
    socket.emit('chatController:getChat', data)
  }

  function getUserMessages(data) {
    console.log('Store: getUserMessages called with:', data)
    socket.emit('messageController:getMessages', data)
  }

  return {
    allChats,
    chatAnalytics,
    messageAnalytics,
    selectedUserChats,
    loading,
    pagination,
    $reset,
    getAllChatsAdmin,
    getChatAnalytics,
    getMessageAnalytics,
    adminDeleteMessage,
    getUserChats,
    getUserChat,
    getUserMessages,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
