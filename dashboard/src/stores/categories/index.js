import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketCategoryStore = defineStore('SocketCategory', () => {
  const categories = ref(null)
  const subCategories = ref(null)
  const category = ref(null)
  const subCategory = ref(null)

  function $reset() {
    categories.value = null
    subCategories.value = null
    category.value = null
    subCategory.value = null
  }

  async function getCategories(data) {
    await socket.emit('categoryController:getCategories', data)
  }

  function getSubCategories() {
    socket.emit('subCategoryController:getSubCategories')
  }

  function getCategory(data) {
    console.log({ data })
    socket.emit('categoryController:getCategory', data)
  }

  function getSubCategory(data) {
    socket.emit('subCategoryController:getSubCategory', data)
  }

  function createCategory(data) {
    socket.emit('categoryController:createCategory', data)
  }

  function createSubCategory(data) {
    socket.emit('subCategoryController:createSubCategory', data)
  }

  function updateCategory(data) {
    socket.emit('categoryController:updateCategory', data)
  }

  function updateSubCategory(data) {
    socket.emit('subCategoryController:updateSubCategory', data)
  }

  function deleteCategory(data) {
    socket.emit('categoryController:deleteCategory', data)
  }

  function deleteSubCategory(data) {
    socket.emit('subCategoryController:deleteSubCategory', data)
  }

  // socketStore.socketObject.emit()
  return {
    categories,
    subCategories,
    category,
    subCategory,
    $reset,
    getCategories,
    getSubCategories,
    getCategory,
    getSubCategory,
    createCategory,
    createSubCategory,
    updateCategory,
    updateSubCategory,
    deleteCategory,
    deleteSubCategory,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
