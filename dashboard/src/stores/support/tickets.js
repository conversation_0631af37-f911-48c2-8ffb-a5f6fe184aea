import { defineS<PERSON> } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketSupportTicketStore = defineStore('SocketSupportTicket', () => {
  const tickets = ref(null)
  const ticket = ref(null)
  const loading = ref(false)
  const error = ref(null)

  function $reset() {
    tickets.value = null
    ticket.value = null
    loading.value = false
    error.value = null
  }

  function getTickets(data) {
    loading.value = true
    error.value = null
    socket.emit('supportTicketController:getTickets', data)
  }

  function getTicket(data) {
    loading.value = true
    error.value = null
    socket.emit('supportTicketController:getTicket', data)
  }

  function createTicket(data) {
    loading.value = true
    error.value = null
    console.log('Creating ticket:', data)
    socket.emit('supportTicketController:createTicket', data)
  }

  function updateTicket(data) {
    loading.value = true
    error.value = null
    console.log('Updating ticket:', data)
    socket.emit('supportTicketController:updateTicket', data)
  }

  function deleteTicket(data) {
    loading.value = true
    error.value = null
    console.log('Deleting ticket:', data)
    socket.emit('supportTicketController:deleteTicket', data)
  }

  function addComment(data) {
    loading.value = true
    error.value = null
    console.log('Adding comment:', data)
    socket.emit('supportTicketController:addComment', data)
  }

  function assignTicket(data) {
    loading.value = true
    error.value = null
    console.log('Assigning ticket:', data)
    socket.emit('supportTicketController:assignTicket', data)
  }

  return {
    tickets,
    ticket,
    loading,
    error,
    $reset,
    getTickets,
    getTicket,
    createTicket,
    updateTicket,
    deleteTicket,
    addComment,
    assignTicket,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
