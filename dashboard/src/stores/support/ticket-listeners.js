import { socket } from '@socket/socket'
import { useSocketSupportTicketStore } from './tickets'
import { useTheme } from 'vuetify'
import { toast } from 'vue3-toastify'

/**
 * Initialize socket event listeners for support tickets
 * This function should be called once when the app is initialized
 */
export function initSupportTicketListeners() {
  const vuetifyTheme = useTheme()

  // Helper function to show toast notifications
  const showToast = (message, type = 'info') => {
    toast(message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type,
    })
  }

  // Get all tickets response
  socket.on('tickets', data => {
    console.log('Received tickets data:', data)
    if (data.status === 'success') {
      const store = useSocketSupportTicketStore()
      store.$patch({
        tickets: data.data,
        loading: false,
        error: null,
      })
    }
  })

  // Get all tickets error
  socket.on('ticketsError', data => {
    console.error('Tickets error:', data)
    const store = useSocketSupportTicketStore()
    store.$patch({
      error: data.message,
      loading: false,
    })
    showToast(data.message, 'error')
  })

  // Get single ticket response
  socket.on('ticket', data => {
    console.log('Received ticket data:', data)
    if (data.status === 'success') {
      const store = useSocketSupportTicketStore()
      store.$patch({
        ticket: data.data,
        loading: false,
        error: null,
      })
    }
  })

  // Get single ticket error
  socket.on('ticketError', data => {
    console.error('Ticket error:', data)
    const store = useSocketSupportTicketStore()
    store.$patch({
      error: data.message,
      loading: false,
    })
    showToast(data.message, 'error')
  })

  // Create ticket response
  socket.on('createTicketComplete', data => {
    console.log('Create ticket complete:', data)
    if (data.status === 'success') {
      const store = useSocketSupportTicketStore()
      
      // Update store state
      store.$patch(state => {
        state.loading = false
        state.error = null
        
        // Add new ticket to the beginning of the list if tickets exist
        if (state.tickets) {
          state.tickets = [data.data, ...state.tickets]
        }
      })
      
      showToast(data.message, 'success')
    }
  })

  // Create ticket error
  socket.on('createTicketError', data => {
    console.error('Create ticket error:', data)
    const store = useSocketSupportTicketStore()
    store.$patch({
      error: data.message,
      loading: false,
    })
    showToast(data.message, 'error')
  })

  // Update ticket response
  socket.on('updateTicketComplete', data => {
    console.log('Update ticket complete:', data)
    if (data.status === 'success') {
      const store = useSocketSupportTicketStore()
      
      // Update store state
      store.$patch(state => {
        state.loading = false
        state.error = null
        state.ticket = data.data
        
        // Update ticket in tickets list if it exists
        if (state.tickets) {
          const index = state.tickets.findIndex(t => t._id === data.data._id)
          if (index !== -1) {
            state.tickets[index] = data.data
          }
        }
      })
      
      showToast(data.message, 'success')
    }
  })

  // Update ticket error
  socket.on('updateTicketError', data => {
    console.error('Update ticket error:', data)
    const store = useSocketSupportTicketStore()
    store.$patch({
      error: data.message,
      loading: false,
    })
    showToast(data.message, 'error')
  })

  // Delete ticket response
  socket.on('deleteTicketComplete', data => {
    console.log('Delete ticket complete:', data)
    if (data.status === 'success') {
      const store = useSocketSupportTicketStore()
      const ticketId = data.ticketId // The backend should send the deleted ticket ID
      
      // Update store state
      store.$patch(state => {
        state.loading = false
        state.error = null
        state.ticket = null
        
        // Remove ticket from tickets list if it exists
        if (state.tickets && ticketId) {
          state.tickets = state.tickets.filter(t => t._id !== ticketId)
        }
      })
      
      showToast(data.message, 'success')
    }
  })

  // Delete ticket error
  socket.on('deleteTicketError', data => {
    console.error('Delete ticket error:', data)
    const store = useSocketSupportTicketStore()
    store.$patch({
      error: data.message,
      loading: false,
    })
    showToast(data.message, 'error')
  })

  // Add comment response
  socket.on('addCommentComplete', data => {
    console.log('Add comment complete:', data)
    if (data.status === 'success') {
      const store = useSocketSupportTicketStore()
      
      // Update store state
      store.$patch(state => {
        state.loading = false
        state.error = null
        state.ticket = data.data
        
        // Update ticket in tickets list if it exists
        if (state.tickets) {
          const index = state.tickets.findIndex(t => t._id === data.data._id)
          if (index !== -1) {
            state.tickets[index] = data.data
          }
        }
      })
      
      showToast(data.message, 'success')
    }
  })

  // Add comment error
  socket.on('addCommentError', data => {
    console.error('Add comment error:', data)
    const store = useSocketSupportTicketStore()
    store.$patch({
      error: data.message,
      loading: false,
    })
    showToast(data.message, 'error')
  })

  // Assign ticket response
  socket.on('assignTicketComplete', data => {
    console.log('Assign ticket complete:', data)
    if (data.status === 'success') {
      const store = useSocketSupportTicketStore()
      
      // Update store state
      store.$patch(state => {
        state.loading = false
        state.error = null
        state.ticket = data.data
        
        // Update ticket in tickets list if it exists
        if (state.tickets) {
          const index = state.tickets.findIndex(t => t._id === data.data._id)
          if (index !== -1) {
            state.tickets[index] = data.data
          }
        }
      })
      
      showToast(data.message, 'success')
    }
  })

  // Assign ticket error
  socket.on('assignTicketError', data => {
    console.error('Assign ticket error:', data)
    const store = useSocketSupportTicketStore()
    store.$patch({
      error: data.message,
      loading: false,
    })
    showToast(data.message, 'error')
  })

  // Notification events
  socket.on('newTicket', data => {
    console.log('New ticket notification:', data)
    showToast(data.message, 'info')
  })

  socket.on('ticketUpdated', data => {
    console.log('Ticket updated notification:', data)
    showToast(data.message, 'info')
  })

  socket.on('ticketCommented', data => {
    console.log('Ticket commented notification:', data)
    showToast(data.message, 'info')
  })

  socket.on('ticketAssigned', data => {
    console.log('Ticket assigned notification:', data)
    showToast(data.message, 'info')
  })
}
