import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketContractTemplateStore = defineStore('SocketContractTemplate', () => {
  const contractTemplates = ref(null)
  const contractTemplate = ref(null)

  function $reset() {
    contractTemplates.value = null
    contractTemplate.value = null
  }

  function getContractTemplates(data) {
    socket.emit('contractTemplateController:getContractTemplates', data)
  }

  function getContractTemplate(data) {
    socket.emit('contractTemplateController:getContractTemplate', data)
  }

  function createContractTemplate(data) {
    socket.emit('contractTemplateController:createContractTemplate', data)
  }

  function updateContractTemplate(data) {
    socket.emit('contractTemplateController:updateContractTemplate', data)
  }

  function deleteContractTemplate(data) {
    socket.emit('contractTemplateController:deleteContractTemplate', data)
  }

  return {
    contractTemplates,
    contractTemplate,
    $reset,
    getContractTemplates,
    getContractTemplate,
    createContractTemplate,
    updateContractTemplate,
    deleteContractTemplate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
