import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketUserThemeSettingsStore = defineStore('SocketUserThemeSettings', () => {
  const themeSettings = ref(null)

  function $reset() {
    themeSettings.value = null
  }

  // User methods
  function getUserThemeSettings(data) {
    console.log('Store: getUserThemeSettings called with:', data)
    socket.emit('themeSettingController:getUserThemeSettings', data)
  }

  function updateUserThemeSettings(data) {
    console.log('Store: updateUserThemeSettings called with:', data)
    socket.emit('themeSettingController:updateUserThemeSettings', data)
  }

  function resetUserThemeSettings(data) {
    console.log('Store: resetUserThemeSettings called with:', data)
    socket.emit('themeSettingController:resetUserThemeSettings', data)
  }

  return {
    themeSettings,
    $reset,
    getUserThemeSettings,
    updateUserThemeSettings,
    resetUserThemeSettings,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
