import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketProfileStore = defineStore('SocketProfile', () => {
  const profile = ref(null)
  const activity = ref(null)

  function $reset() {
    profile.value = null
  }

  function getProfiles(data) {
    socket.emit('profileController:getProfiles', data)
  }

  function getProfile(data) {
    socket.emit('profileController:getProfile', data)
  }

  function updateProfile(data) {
    console.log({ data })
    socket.emit('profileController:updateProfile', data)
  }

  function uploadProfileImage(data) {
    console.log({ data })
    socket.emit('profileController:uploadProfileImage', data)
  }

  function uploadCoverImage(data) {
    console.log({ data })
    socket.emit('profileController:uploadCoverImage', data)
  }

  function getActivity(data) {
    console.log({ data })
    socket.emit('profileController:activity', data)
  }

  return {
    profile,
    activity,
    $reset,
    getProfiles,
    getProfile,
    updateProfile,
    uploadProfileImage,
    uploadCoverImage,
    getActivity,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
