import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketDashboardStore = defineStore('SocketDashboard', () => {
  const totalInvoices = ref(null)
  const earningsWidgets = ref(null)
  const earningsReports = ref(null)
  const earningsPastYears = ref(null)
  const jobStatuses = ref(null)
  const weeklyMetrics = ref(null)
  const monthlyMetrics = ref(null)
  const profitLoss = ref(null)
  const weeklyProfitLoss = ref(null)
  const monthlyProfitLoss = ref(null)
  const yearlyProfitLoss = ref(null)
  const totalSales = ref(null)
  const weeklySales = ref(null)

  function $reset() {
    totalInvoices.value = null
    earningsWidgets.value = null
    earningsReports.value = null
    earningsPastYears.value = null
    jobStatuses.value = null
    weeklyMetrics.value = null
    monthlyMetrics.value = null
    profitLoss.value = null
    weeklyProfitLoss.value = null
    monthlyProfitLoss.value = null
    yearlyProfitLoss.value = null
    totalSales.value = null
    weeklySales.value = null
  }

  function getWeeklyInvoiceTotals(data) {
    socket.emit('dashboardController:getWeeklyInvoiceTotals', data)
  }

  function getEarningsWidgets(data) {
    socket.emit('dashboardController:earningsWidgets', data)
  }

  function getEarningsReports(data) {
    socket.emit('dashboardController:earningsReports', data)
  }

  function getEarningsPastYears(data) {
    socket.emit('dashboardController:earningsPastYears', data)
  }

  function getJobStatuses(data) {
    socket.emit('dashboardController:jobStatuses', data)
  }

  function getWeeklyMetrics(data) {
    socket.emit('dashboardController:getWeeklyMetrics', data)
  }

  function getMonthlyMetrics(data) {
    socket.emit('dashboardController:getMonthlyMetrics', data)
  }

  function getProfitLossReport(data) {
    socket.emit('dashboardController:getProfitLossReport', data)
  }

  function getTotalSales(data) {
    socket.emit('dashboardController:getTotalSales', data)
  }

  function getWeeklySalesMetrics(data) {
    socket.emit('dashboardController:getWeeklySalesMetrics', data)
  }

  function getMonthlyProfitLossReport(data) {
    socket.emit('dashboardController:getMonthlyProfitLossReport', data)
  }

  function getWeeklyProfitLossReport(data) {
    socket.emit('dashboardController:getWeeklyProfitLossReport', data)
  }

  function getYearlyProfitLossReport(data) {
    socket.emit('dashboardController:getYearlyProfitLossReport', data)
  }

  return {
    totalInvoices,
    earningsWidgets,
    earningsReports,
    earningsPastYears,
    jobStatuses,
    weeklyMetrics,
    monthlyMetrics,
    profitLoss,
    weeklyProfitLoss,
    monthlyProfitLoss,
    yearlyProfitLoss,
    totalSales,
    weeklySales,
    $reset,
    getWeeklyInvoiceTotals,
    getEarningsWidgets,
    getEarningsReports,
    getEarningsPastYears,
    getJobStatuses,
    getWeeklyMetrics,
    getMonthlyMetrics,
    getProfitLossReport,
    getWeeklyProfitLossReport,
    getMonthlyProfitLossReport,
    getYearlyProfitLossReport,
    getTotalSales,
    getWeeklySalesMetrics,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
