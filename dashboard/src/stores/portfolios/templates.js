import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketPortfolioTemplateStore = defineStore('SocketPortfolioTemplate', () => {
  const templates = ref(null)
  const template = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 10,
    total: 0,
  })

  function $reset() {
    templates.value = null
    template.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 10,
      total: 0,
    }
  }

  // Template methods
  function getAllPortfolioTemplates(data) {
    socket.emit('portfolioTemplateController:getAllPortfolioTemplates', data)
  }

  function getPortfolioTemplate(data) {
    socket.emit('portfolioTemplateController:getPortfolioTemplate', data)
  }

  function createPortfolioTemplate(data) {
    socket.emit('portfolioTemplateController:createPortfolioTemplate', data)
  }

  function updatePortfolioTemplate(data) {
    socket.emit('portfolioTemplateController:updatePortfolioTemplate', data)
  }

  function deletePortfolioTemplate(data) {
    socket.emit('portfolioTemplateController:deletePortfolioTemplate', data)
  }

  function clonePortfolioTemplate(data) {
    socket.emit('portfolioTemplateController:clonePortfolioTemplate', data)
  }

  return {
    templates,
    template,
    pagination,
    $reset,
    getAllPortfolioTemplates,
    getPortfolioTemplate,
    createPortfolioTemplate,
    updatePortfolioTemplate,
    deletePortfolioTemplate,
    clonePortfolioTemplate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
