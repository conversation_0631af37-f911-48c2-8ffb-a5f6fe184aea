import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketPortfolioStore = defineStore('SocketPortfolio', () => {
  const portfolios = ref(null)
  const portfolio = ref(null)

  function $reset() {
    portfolios.value = null
    portfolio.value = null
  }

  function getPortfolios(data) {
    socket.emit('portfolioController:getPortfolios', data)
  }

  function getPortfolio(data) {
    socket.emit('portfolioController:getPortfolio', data)
  }

  function getPortfolioBySlug(data) {
    socket.emit('portfolioController:getPortfolioBySlug', data)
  }

  function createPortfolio(data) {
    socket.emit('portfolioController:createPortfolio', data)
  }

  function updatePortfolio(data) {
    socket.emit('portfolioController:updatePortfolio', data)
  }

  function deletePortfolio(data) {
    socket.emit('portfolioController:deletePortfolio', data)
  }

  function getPortfolioImages(data) {
    socket.emit('portfolioController:getPortfolioImages', data)
  }

  return {
    portfolios,
    portfolio,
    $reset,
    getPortfolios,
    getPortfolio,
    getPortfolioBySlug,
    createPortfolio,
    updatePortfolio,
    deletePortfolio,
    getPortfolioImages,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
