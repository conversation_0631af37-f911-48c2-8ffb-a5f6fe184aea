import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketCollaboratorStore = defineStore('SocketCollaborator', () => {
  const collaborators = ref(null)
  const collaborator = ref(null)

  function $reset() {
    collaborators.value = null
    collaborator.value = null
  }

  function getAllCollaborators(data) {
    socket.emit('collaboratorController:getAllCollaborators', data)
  }

  function getCollaborator(data) {
    socket.emit('collaboratorController:getCollaborator', data)
  }

  function createCollaborator(data) {
    socket.emit('collaboratorController:createCollaborator', data)
  }

  function updateCollaborator(data) {
    socket.emit('collaboratorController:updateCollaborator', data)
  }

  function deleteCollaborator(data) {
    socket.emit('collaboratorController:deleteCollaborator', data)
  }

  return {
    collaborators,
    collaborator,
    $reset,
    getAllCollaborators,
    getCollaborator,
    createCollaborator,
    updateCollaborator,
    deleteCollaborator,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
