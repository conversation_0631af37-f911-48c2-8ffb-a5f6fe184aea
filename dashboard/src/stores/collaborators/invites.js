import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketCollaboratorInviteStore = defineStore('SocketCollaboratorInvite', () => {
  const invites = ref(null)
  const invite = ref(null)

  function $reset() {
    invites.value = null
    invite.value = null
  }

  function getAllInvites(data) {
    socket.emit('collaboratorInviteController:getAllInvites', data)
  }

  function getInvite(data) {
    socket.emit('collaboratorInviteController:getInvite', data)
  }

  function createInvite(data) {
    socket.emit('collaboratorInviteController:createInvite', data)
  }

  function updateInvite(data) {
    socket.emit('collaboratorInviteController:updateInvite', data)
  }

  function deleteInvite(data) {
    socket.emit('collaboratorInviteController:deleteInvite', data)
  }

  function acceptInvite(data) {
    socket.emit('collaboratorInviteController:acceptInvite', data)
  }

  return {
    invites,
    invite,
    $reset,
    getAllInvites,
    getInvite,
    createInvite,
    updateInvite,
    deleteInvite,
    acceptInvite,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
