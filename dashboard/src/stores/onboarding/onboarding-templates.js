import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useSocketOnboardingTemplateStore = defineStore('SocketOnboardingTemplate', () => {
  const onboardingTemplates = ref(null)
  const onboardingTemplate = ref(null)

  function $reset() {
    onboardingTemplates.value = null
    onboardingTemplate.value = null
  }

  function getOnboardingTemplates(data) {
    socket.emit('onboardingTemplateController:getAllOnboardingTemplates', data)
  }

  function getOnboardingTemplate(data) {
    socket.emit('onboardingTemplateController:getOnboardingTemplate', data)
  }

  function createOnboardingTemplate(data) {
    socket.emit('onboardingTemplateController:createOnboardingTemplate', data)
  }

  function updateOnboardingTemplate(data) {
    socket.emit('onboardingTemplateController:updateOnboardingTemplate', data)
  }

  function deleteOnboardingTemplate(data) {
    socket.emit('onboardingTemplateController:deleteOnboardingTemplate', data)
  }

  return {
    onboardingTemplates,
    onboardingTemplate,
    $reset,
    getOnboardingTemplates,
    getOnboardingTemplate,
    createOnboardingTemplate,
    updateOnboardingTemplate,
    deleteOnboardingTemplate,
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
