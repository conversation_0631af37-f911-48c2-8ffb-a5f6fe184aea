import { socket } from '@socket/socket'
import { defineStore } from 'pinia'

export const useSubscriptionSocketStore = defineStore('SubscriptionSocket', () => {
  const subscriptionPackages = ref(null)
  const subscriptionPackage = ref(null)
  const userSubscriptionDetails = ref(null)
  const subscriptionPlans = ref(null)

  function $reset() {
    subscriptionPackages.value = null
    subscriptionPackage.value = null
    userSubscriptionDetails.value = null
    subscriptionPlans.value = null
  }

  function getFrontendSubscriptionPackages() {
    socket.emit('subscriptionController:getFrontendSubscriptionPackages')
  }

  function getSubscriptionPackages(data) {
    socket.emit('subscriptionController:getSubscriptionPackages', data)
  }

  function getSubscriptionPackage(data) {
    socket.emit('subscriptionController:getSubscriptionPackage', data)
  }

  function createSubscriptionPackage(data) {
    socket.emit('subscriptionController:createSubscriptionPackage', data)
  }

  function updateSubscriptionPackage(data) {
    socket.emit('subscriptionController:updateSubscriptionPackage', data)
  }

  function deleteSubscriptionPackage(data) {
    socket.emit('subscriptionController:deleteSubscriptionPackage', data)
  }

  function createPaystackSubscription(data) {
    console.log('Paystack', { data })
    socket.emit('paystackController:createPaystackSubscription', data)
  }

  function startTrial(data) {
    socket.emit('startTrial', {
      userId: user._id,
      planCode: 'PLN_lt6ibafwu0fa26l',
      paystackCustomerCode: '1394294',
    })
  }

  function getUserSubscriptionDetails(data) {
    socket.emit('userSubscriptionController:getUserSubscriptionDetails', data)
  }

  function getSubscriptionPlansFromFile(data = {}) {
    socket.emit('subscriptionController:getSubscriptionPlansFromFile', data)
  }

  function cancelUserSubscription(data) {
    socket.emit('userSubscriptionController:cancelUserSubscription', data)
  }

  function updateUserSubscription(data) {
    socket.emit('userSubscriptionController:updateUserSubscription', data)
  }

  function upgradeFromTrial(data) {
    socket.emit('paystackController:upgradeFromTrial', data)
  }

  return {
    subscriptionPackages,
    subscriptionPackage,
    userSubscriptionDetails,
    subscriptionPlans,
    $reset,
    getFrontendSubscriptionPackages,
    getSubscriptionPackages,
    getSubscriptionPackage,
    createSubscriptionPackage,
    updateSubscriptionPackage,
    deleteSubscriptionPackage,
    createPaystackSubscription,
    startTrial,
    getUserSubscriptionDetails,
    getSubscriptionPlansFromFile,
    cancelUserSubscription,
    updateUserSubscription,
    upgradeFromTrial,
  }
})
