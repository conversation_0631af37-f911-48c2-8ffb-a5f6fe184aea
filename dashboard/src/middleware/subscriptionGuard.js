import { useSocketStore } from '@stores/auth'
import { useSubscriptionSocketStore } from '@stores/subscription'

// Routes that don't require subscription check
const publicRoutes = [
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/subscription',
  '/subscription/callback',
  '/subscription/success',
  '/subscription/upgrade-success',
  '/auth/verify-email'
]

// Routes that are allowed even with expired subscription
const allowedExpiredRoutes = [
  '/subscription',
  '/user-settings',
  '/profile'
]

export async function subscriptionGuard(to, from, next) {
  // Skip check for public routes
  if (publicRoutes.some(route => to.path.startsWith(route))) {
    return next()
  }

  // For now, allow all authenticated routes
  // The subscription blocker in the layout will handle the actual blocking
  // This prevents route navigation issues while still allowing the layout to block access
  return next()
}

// Helper function to get subscription details
async function getSubscriptionDetails(userId, subscriptionStore) {
  // For now, we'll skip the async check in the route guard
  // The subscription blocker in the layout will handle blocking
  // This prevents route guard from blocking navigation
  return null
}

// Helper function to check if subscription is expired
function checkIfExpired(subscription) {
  if (!subscription) return false

  const now = new Date()

  // Check if explicitly marked as expired
  if (subscription.status === 'expired') {
    return true
  }

  // Check if trial has expired
  if (subscription.status === 'trial' && subscription.trialEndsAt) {
    return new Date(subscription.trialEndsAt) <= now
  }

  // Check if paid subscription has expired
  if (subscription.status === 'active' && subscription.endsAt) {
    return new Date(subscription.endsAt) <= now
  }

  return false
}
