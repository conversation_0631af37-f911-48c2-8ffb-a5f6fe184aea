<script setup>
const props = defineProps({
  rateCard: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits([
  'edit',
  'view',
  'delete',
])

// Methods
function handleEdit() {
  emit('edit', props.rateCard)
}

function handleView() {
  emit('view', props.rateCard)
}

function handleDelete() {
  emit('delete', props.rateCard)
}

function formatDate(date) {
  return new Date(date).toLocaleDateString()
}

function getTypeColor(type) {
  const colors = {
    Photography: 'primary',
    Video: 'success',
    Event: 'info',
    Commercial: 'warning',
    Wedding: 'pink',
    Portrait: 'purple',
    Product: 'orange',
    Custom: 'grey',
  }

  
  return colors[type] || 'grey'
}

function getTypeIcon(type) {
  const icons = {
    Photography: 'tabler-camera',
    Video: 'tabler-video',
    Event: 'tabler-calendar-event',
    Commercial: 'tabler-building',
    Wedding: 'tabler-heart',
    Portrait: 'tabler-user',
    Product: 'tabler-package',
    Custom: 'tabler-settings',
  }

  
  return icons[type] || 'tabler-credit-card'
}

function formatCurrency(amount) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount || 0)
}
</script>

<template>
  <VCard
    class="rate-card-card"
    hover
    @click="handleView"
  >
    <!-- Card Header -->
    <VCardTitle class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <VIcon
          :icon="getTypeIcon(rateCard.type)"
          class="me-2"
          :color="getTypeColor(rateCard.type)"
        />
        <span class="text-truncate">{{ rateCard.name }}</span>
      </div>
      
      <div class="d-flex gap-1">
        <VChip
          v-if="rateCard.isDefault"
          color="success"
          size="x-small"
          variant="tonal"
        >
          Default
        </VChip>
        <VChip
          v-if="!rateCard.isActive"
          color="error"
          size="x-small"
          variant="tonal"
        >
          Inactive
        </VChip>
      </div>
    </VCardTitle>

    <!-- Card Content -->
    <VCardText>
      <div class="d-flex align-center justify-space-between mb-3">
        <VChip
          :color="getTypeColor(rateCard.type)"
          size="small"
          variant="tonal"
        >
          {{ rateCard.type }}
        </VChip>
        
        <div
          v-if="rateCard.baseRate"
          class="d-flex align-center gap-2"
        >
          <VIcon
            icon="tabler-currency-dollar"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ formatCurrency(rateCard.baseRate) }}
          </span>
        </div>
      </div>

      <p class="text-body-2 text-medium-emphasis mb-3 text-truncate-3">
        {{ rateCard.description || 'No description provided' }}
      </p>

      <!-- Services Count -->
      <div class="d-flex align-center justify-space-between mb-3">
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-list"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ rateCard.services?.length || 0 }} services
          </span>
        </div>
        
        <div
          v-if="rateCard.currency"
          class="d-flex align-center gap-2"
        >
          <VIcon
            icon="tabler-currency"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ rateCard.currency }}
          </span>
        </div>
      </div>

      <!-- Date and Identity -->
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-calendar"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ formatDate(rateCard.createdAt) }}
          </span>
        </div>
        
        <div
          v-if="rateCard.identity"
          class="d-flex align-center gap-2"
        >
          <VIcon
            icon="tabler-user"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ rateCard.identity.name }}
          </span>
        </div>
      </div>

      <!-- Usage Count -->
      <div
        v-if="rateCard.usageCount > 0"
        class="d-flex align-center gap-2 mt-2"
      >
        <VIcon
          icon="tabler-eye"
          size="16"
          class="text-medium-emphasis"
        />
        <span class="text-caption text-medium-emphasis">
          Used {{ rateCard.usageCount }} times
        </span>
      </div>
    </VCardText>

    <!-- Card Actions -->
    <VCardActions>
      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-eye"
        @click.stop="handleView"
      >
        View
      </VBtn>
      
      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-edit"
        @click.stop="handleEdit"
      >
        Edit
      </VBtn>
      
      <VSpacer />
      
      <VBtn
        variant="text"
        size="small"
        color="error"
        icon
        @click.stop="handleDelete"
      >
        <VIcon icon="tabler-trash" />
      </VBtn>
    </VCardActions>
  </VCard>
</template>

<style lang="scss" scoped>
.rate-card-card {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
