<script setup>
const props = defineProps({
  template: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits([
  'edit',
  'view',
  'delete',
])

// Methods
function handleEdit() {
  emit('edit', props.template)
}

function handleView() {
  emit('view', props.template)
}

function handleDelete() {
  emit('delete', props.template)
}

function formatDate(date) {
  return new Date(date).toLocaleDateString()
}

function getTypeColor(type) {
  const colors = {
    Photography: 'primary',
    Video: 'success',
    Event: 'info',
    Commercial: 'warning',
    Wedding: 'pink',
    Portrait: 'purple',
    Product: 'orange',
    Custom: 'grey',
  }

  
  return colors[type] || 'grey'
}

function getTypeIcon(type) {
  const icons = {
    Photography: 'tabler-camera',
    Video: 'tabler-video',
    Event: 'tabler-calendar-event',
    Commercial: 'tabler-building',
    Wedding: 'tabler-heart',
    Portrait: 'tabler-user',
    Product: 'tabler-package',
    Custom: 'tabler-settings',
  }

  
  return icons[type] || 'tabler-template'
}

function getServiceCount(services) {
  return Array.isArray(services) ? services.length : 0
}
</script>

<template>
  <VCard
    class="template-card"
    hover
    @click="handleView"
  >
    <!-- Card Header -->
    <VCardTitle class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <VIcon
          :icon="getTypeIcon(template.type)"
          class="me-2"
          :color="getTypeColor(template.type)"
        />
        <span class="text-truncate">{{ template.templateName }}</span>
      </div>
      
      <div class="d-flex gap-1">
        <VChip
          v-if="template.isDefault"
          color="success"
          size="x-small"
          variant="tonal"
        >
          Default
        </VChip>
        <VChip
          v-if="!template.isActive"
          color="error"
          size="x-small"
          variant="tonal"
        >
          Inactive
        </VChip>
      </div>
    </VCardTitle>

    <!-- Card Content -->
    <VCardText>
      <div class="d-flex align-center justify-space-between mb-3">
        <VChip
          :color="getTypeColor(template.type)"
          size="small"
          variant="tonal"
        >
          {{ template.type }}
        </VChip>
        
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-template"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            Template
          </span>
        </div>
      </div>

      <p class="text-body-2 text-medium-emphasis mb-3 text-truncate-3">
        {{ template.description || 'No description provided' }}
      </p>

      <!-- Services and Usage -->
      <div class="d-flex align-center justify-space-between mb-3">
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-list"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ getServiceCount(template.services) }} services
          </span>
        </div>
        
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-eye"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ template.usageCount || 0 }} uses
          </span>
        </div>
      </div>

      <!-- Tags -->
      <div
        v-if="template.tags && template.tags.length > 0"
        class="mb-3"
      >
        <div class="d-flex flex-wrap gap-1">
          <VChip
            v-for="tag in template.tags.slice(0, 3)"
            :key="tag"
            size="x-small"
            variant="outlined"
          >
            {{ tag }}
          </VChip>
          <VChip
            v-if="template.tags.length > 3"
            size="x-small"
            variant="outlined"
            color="primary"
          >
            +{{ template.tags.length - 3 }}
          </VChip>
        </div>
      </div>

      <!-- Date and Identity -->
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-calendar"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ formatDate(template.createdAt) }}
          </span>
        </div>
        
        <div
          v-if="template.identity"
          class="d-flex align-center gap-2"
        >
          <VIcon
            icon="tabler-user"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ template.identity.name }}
          </span>
        </div>
      </div>

      <!-- Version -->
      <div
        v-if="template.version > 1"
        class="d-flex align-center gap-2 mt-2"
      >
        <VIcon
          icon="tabler-versions"
          size="16"
          class="text-medium-emphasis"
        />
        <span class="text-caption text-medium-emphasis">
          Version {{ template.version }}
        </span>
      </div>
    </VCardText>

    <!-- Card Actions -->
    <VCardActions>
      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-eye"
        @click.stop="handleView"
      >
        View
      </VBtn>
      
      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-edit"
        @click.stop="handleEdit"
      >
        Edit
      </VBtn>
      
      <VSpacer />
      
      <VBtn
        variant="text"
        size="small"
        color="error"
        icon
        @click.stop="handleDelete"
      >
        <VIcon icon="tabler-trash" />
      </VBtn>
    </VCardActions>
  </VCard>
</template>

<style lang="scss" scoped>
.template-card {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
