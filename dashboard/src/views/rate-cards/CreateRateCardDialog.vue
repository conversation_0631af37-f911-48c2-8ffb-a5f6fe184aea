<script setup>
import { ref, toRefs, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketRateCardStore } from '@stores/rate-cards/index'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  defaultType: {
    type: String,
    default: 'Photography',
  },
  selectedIdentity: {
    type: String,
    default: null,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible, defaultType, selectedIdentity } = toRefs(props)

const store = useSocketStore()
const rateCardStore = useSocketRateCardStore()
const { user } = storeToRefs(store)

// Form data
const rateCardForm = ref({
  name: '',
  description: '',
  type: defaultType.value,
  identity: selectedIdentity.value || (user.value.identities?.[0]?._id || ''),
  rates: [],
  baseRate: 0,
  currency: 'USD',
  tags: [],
  isActive: true,
  isDefault: false,
})

// New rate form
const newRate = ref({
  serviceName: '',
  description: '',
  rate: 0,
  unit: 'hour',
  currency: 'USD',
  category: '',
  isActive: true,
})

// Tag input
const tagInput = ref('')

// Identity options
const identityOptions = computed(() => {
  if (!user.value.identities) return []
  
  return user.value.identities.map(identity => ({
    value: identity._id,
    title: identity.name,
    subtitle: identity.email || identity.phone,
  }))
})

// Units for rates
const units = [
  { value: 'hour', title: 'Per Hour' },
  { value: 'day', title: 'Per Day' },
  { value: 'project', title: 'Per Project' },
  { value: 'item', title: 'Per Item' },
  { value: 'session', title: 'Per Session' },
  { value: 'event', title: 'Per Event' },
  { value: 'custom', title: 'Custom' },
]

// Add rate
function addRate() {
  if (newRate.value.serviceName && newRate.value.rate > 0) {
    rateCardForm.value.rates.push({ ...newRate.value })
    resetNewRate()
  }
}

// Remove rate
function removeRate(index) {
  rateCardForm.value.rates.splice(index, 1)
}

// Reset new rate form
function resetNewRate() {
  newRate.value = {
    serviceName: '',
    description: '',
    rate: 0,
    unit: 'hour',
    currency: rateCardForm.value.currency,
    category: '',
    isActive: true,
  }
}

// Add tag
function addTag() {
  const tag = tagInput.value.trim()
  if (tag && !rateCardForm.value.tags.includes(tag)) {
    rateCardForm.value.tags.push(tag)
    tagInput.value = ''
  }
}

// Remove tag
function removeTag(index) {
  rateCardForm.value.tags.splice(index, 1)
}

// Handle tag input keypress
function handleTagKeypress(event) {
  if (event.key === 'Enter') {
    event.preventDefault()
    addTag()
  }
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  rateCardForm.value = {
    name: '',
    description: '',
    type: defaultType.value,
    identity: selectedIdentity.value || (user.value.identities?.[0]?._id || ''),
    rates: [],
    baseRate: 0,
    currency: 'USD',
    tags: [],
    isActive: true,
    isDefault: false,
  }
  resetNewRate()
  tagInput.value = ''
}

// Form validation
const isFormValid = computed(() => {
  return rateCardForm.value.name.trim() !== '' && rateCardForm.value.identity !== ''
})

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  rateCardStore.createRateCard({
    user: user.value._id,
    identity: rateCardForm.value.identity,
    name: rateCardForm.value.name,
    description: rateCardForm.value.description,
    type: rateCardForm.value.type,
    rates: rateCardForm.value.rates,
    baseRate: rateCardForm.value.baseRate,
    currency: rateCardForm.value.currency,
    tags: rateCardForm.value.tags,
    isActive: rateCardForm.value.isActive,
    isDefault: rateCardForm.value.isDefault,
  })
}

// Watch for currency changes to update new rate currency
watch(() => rateCardForm.value.currency, newCurrency => {
  newRate.value.currency = newCurrency
})

// Watch for default type changes
watch(defaultType, newType => {
  if (newType && rateCardForm.value.type !== newType) {
    rateCardForm.value.type = newType
  }
}, { immediate: true })
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="600"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Create Rate Card</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="rateCardForm.name"
                label="Rate Card Name"
                placeholder="Enter a descriptive name"
                required
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="rateCardForm.description"
                label="Description"
                placeholder="Describe this rate card"
                rows="3"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="rateCardForm.type"
                :items="['Photography', 'Video', 'Event', 'Commercial', 'Wedding', 'Portrait', 'Product', 'Custom']"
                label="Type"
                prepend-inner-icon="tabler-category"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="rateCardForm.identity"
                :items="identityOptions"
                item-title="title"
                item-value="value"
                label="Identity"
                prepend-inner-icon="tabler-user"
                :rules="[v => !!v || 'Identity is required']"
              >
                <template
                  #item="
                    /* eslint-disable-next-line vue/no-template-shadow */
                    { props, item }"
                >
                  <VListItem v-bind="props">
                    <template #prepend>
                      <VIcon icon="tabler-user" />
                    </template>
                    <VListItemTitle>{{ item.raw.title }}</VListItemTitle>
                    <VListItemSubtitle v-if="item.raw.subtitle">
                      {{ item.raw.subtitle }}
                    </VListItemSubtitle>
                  </VListItem>
                </template>
              </VSelect>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model.number="rateCardForm.baseRate"
                label="Base Rate"
                type="number"
                prepend-inner-icon="tabler-currency-dollar"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="rateCardForm.currency"
                :items="['USD', 'EUR', 'GBP', 'CAD', 'AUD']"
                label="Currency"
                prepend-inner-icon="tabler-currency"
              />
            </VCol>

            <!-- Tags Section -->
            <VCol cols="12">
              <VLabel class="mb-2">
                Tags
              </VLabel>
              <div class="d-flex gap-2 mb-2">
                <AppTextField
                  v-model="tagInput"
                  placeholder="Add a tag"
                  density="compact"
                  @keypress="handleTagKeypress"
                />
                <VBtn
                  color="primary"
                  variant="outlined"
                  size="small"
                  @click="addTag"
                >
                  Add
                </VBtn>
              </div>
              <div class="d-flex flex-wrap gap-2">
                <VChip
                  v-for="(tag, index) in rateCardForm.tags"
                  :key="index"
                  size="small"
                  closable
                  @click:close="removeTag(index)"
                >
                  {{ tag }}
                </VChip>
              </div>
            </VCol>

            <!-- Rates Section -->
            <VCol cols="12">
              <VLabel class="mb-2">
                Service Rates
              </VLabel>

              <!-- Add New Rate -->
              <VCard
                variant="outlined"
                class="mb-4"
              >
                <VCardTitle class="text-subtitle-1">
                  Add Service Rate
                </VCardTitle>
                <VCardText>
                  <VRow>
                    <VCol
                      cols="12"
                      md="6"
                    >
                      <AppTextField
                        v-model="newRate.serviceName"
                        label="Service Name"
                        placeholder="e.g., Portrait Session"
                        density="compact"
                      />
                    </VCol>

                    <VCol
                      cols="12"
                      md="6"
                    >
                      <AppTextField
                        v-model="newRate.category"
                        label="Category"
                        placeholder="e.g., Photography"
                        density="compact"
                      />
                    </VCol>

                    <VCol cols="12">
                      <AppTextarea
                        v-model="newRate.description"
                        label="Description"
                        placeholder="Describe this service"
                        rows="2"
                        density="compact"
                      />
                    </VCol>

                    <VCol
                      cols="12"
                      md="4"
                    >
                      <AppTextField
                        v-model.number="newRate.rate"
                        label="Rate"
                        type="number"
                        density="compact"
                      />
                    </VCol>

                    <VCol
                      cols="12"
                      md="4"
                    >
                      <VSelect
                        v-model="newRate.unit"
                        :items="units"
                        item-title="title"
                        item-value="value"
                        label="Unit"
                        density="compact"
                      />
                    </VCol>

                    <VCol
                      cols="12"
                      md="4"
                    >
                      <VSelect
                        v-model="newRate.currency"
                        :items="['USD', 'EUR', 'GBP', 'CAD', 'AUD']"
                        label="Currency"
                        density="compact"
                      />
                    </VCol>
                  </VRow>

                  <div class="d-flex justify-end mt-2">
                    <VBtn
                      color="primary"
                      size="small"
                      @click="addRate"
                    >
                      Add Rate
                    </VBtn>
                  </div>
                </VCardText>
              </VCard>

              <!-- Existing Rates -->
              <div v-if="rateCardForm.rates.length > 0">
                <VCard
                  v-for="(rate, index) in rateCardForm.rates"
                  :key="index"
                  variant="outlined"
                  class="mb-2"
                >
                  <VCardText class="py-2">
                    <div class="d-flex align-center justify-space-between">
                      <div>
                        <div class="font-weight-medium">
                          {{ rate.serviceName }}
                        </div>
                        <div class="text-caption text-medium-emphasis">
                          {{ rate.rate }} {{ rate.currency }} {{ rate.unit }}
                          <span v-if="rate.category"> • {{ rate.category }}</span>
                        </div>
                        <div
                          v-if="rate.description"
                          class="text-caption"
                        >
                          {{ rate.description }}
                        </div>
                      </div>
                      <VBtn
                        icon
                        size="small"
                        color="error"
                        variant="text"
                        @click="removeRate(index)"
                      >
                        <VIcon icon="tabler-trash" />
                      </VBtn>
                    </div>
                  </VCardText>
                </VCard>
              </div>
            </VCol>

            <VCol cols="12">
              <VRow>
                <VCol
                  cols="12"
                  md="6"
                >
                  <VCheckbox
                    v-model="rateCardForm.isActive"
                    label="Active rate card"
                    density="compact"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <VCheckbox
                    v-model="rateCardForm.isDefault"
                    label="Set as default"
                    density="compact"
                  />
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="outlined"
          @click="handleDialogClose"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          Create Rate Card
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
