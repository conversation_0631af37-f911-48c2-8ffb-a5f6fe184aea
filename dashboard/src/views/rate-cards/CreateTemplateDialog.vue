<script setup>
import { ref, toRefs, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketRateCardTemplateStore } from '@stores/rate-cards/templates'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  defaultType: {
    type: String,
    default: 'Photography',
  },
  selectedIdentity: {
    type: String,
    default: null,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible, defaultType, selectedIdentity } = toRefs(props)

const store = useSocketStore()
const templateStore = useSocketRateCardTemplateStore()
const { user } = storeToRefs(store)

// Form data
const templateForm = ref({
  templateName: '',
  description: '',
  type: defaultType.value,
  identity: selectedIdentity.value || (user.value.identities?.[0]?._id || ''),
  services: [],
  defaultCurrency: 'USD',
  tags: [],
  isActive: true,
  isDefault: false,
})

// New service form
const newService = ref({
  serviceName: '',
  description: '',
  defaultRate: 0,
  unit: 'hour',
  category: '',
  isRequired: false,
})

// Tag input
const tagInput = ref('')

// Identity options
const identityOptions = computed(() => {
  if (!user.value.identities) return []
  
  return user.value.identities.map(identity => ({
    value: identity._id,
    title: identity.name,
    subtitle: identity.email || identity.phone,
  }))
})

// Units for services
const units = [
  { value: 'hour', title: 'Per Hour' },
  { value: 'day', title: 'Per Day' },
  { value: 'project', title: 'Per Project' },
  { value: 'item', title: 'Per Item' },
  { value: 'session', title: 'Per Session' },
  { value: 'event', title: 'Per Event' },
  { value: 'custom', title: 'Custom' },
]

// Add service
function addService() {
  if (newService.value.serviceName) {
    templateForm.value.services.push({ ...newService.value })
    resetNewService()
  }
}

// Remove service
function removeService(index) {
  templateForm.value.services.splice(index, 1)
}

// Reset new service form
function resetNewService() {
  newService.value = {
    serviceName: '',
    description: '',
    defaultRate: 0,
    unit: 'hour',
    category: '',
    isRequired: false,
  }
}

// Add tag
function addTag() {
  const tag = tagInput.value.trim()
  if (tag && !templateForm.value.tags.includes(tag)) {
    templateForm.value.tags.push(tag)
    tagInput.value = ''
  }
}

// Remove tag
function removeTag(index) {
  templateForm.value.tags.splice(index, 1)
}

// Handle tag input keypress
function handleTagKeypress(event) {
  if (event.key === 'Enter') {
    event.preventDefault()
    addTag()
  }
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  templateForm.value = {
    templateName: '',
    description: '',
    type: defaultType.value,
    identity: selectedIdentity.value || (user.value.identities?.[0]?._id || ''),
    services: [],
    defaultCurrency: 'USD',
    tags: [],
    isActive: true,
    isDefault: false,
  }
  resetNewService()
  tagInput.value = ''
}

// Form validation
const isFormValid = computed(() => {
  return templateForm.value.templateName.trim() !== '' && templateForm.value.identity !== ''
})

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  templateStore.createRateCardTemplate({
    user: user.value._id,
    identity: templateForm.value.identity,
    templateName: templateForm.value.templateName,
    description: templateForm.value.description,
    type: templateForm.value.type,
    services: templateForm.value.services,
    defaultCurrency: templateForm.value.defaultCurrency,
    tags: templateForm.value.tags,
    isActive: templateForm.value.isActive,
    isDefault: templateForm.value.isDefault,
  })
}

// Watch for default type changes
watch(defaultType, newType => {
  if (newType && templateForm.value.type !== newType) {
    templateForm.value.type = newType
  }
}, { immediate: true })
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="700"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Create Rate Card Template</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="templateForm.templateName"
                label="Template Name"
                placeholder="Enter a descriptive name"
                required
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="templateForm.description"
                label="Description"
                placeholder="Describe this template"
                rows="3"
              />
            </VCol>

            <VCol
              cols="12"
              md="4"
            >
              <VSelect
                v-model="templateForm.type"
                :items="['Photography', 'Video', 'Event', 'Commercial', 'Wedding', 'Portrait', 'Product', 'Custom']"
                label="Type"
                prepend-inner-icon="tabler-category"
              />
            </VCol>

            <VCol
              cols="12"
              md="4"
            >
              <VSelect
                v-model="templateForm.identity"
                :items="identityOptions"
                item-title="title"
                item-value="value"
                label="Identity"
                prepend-inner-icon="tabler-user"
                :rules="[v => !!v || 'Identity is required']"
              >
                <template
                  #item="
                    /* eslint-disable-next-line vue/no-template-shadow */
                    { props, item }"
                >
                  <VListItem v-bind="props">
                    <template #prepend>
                      <VIcon icon="tabler-user" />
                    </template>
                    <VListItemTitle>{{ item.raw.title }}</VListItemTitle>
                    <VListItemSubtitle v-if="item.raw.subtitle">
                      {{ item.raw.subtitle }}
                    </VListItemSubtitle>
                  </VListItem>
                </template>
              </VSelect>
            </VCol>

            <VCol
              cols="12"
              md="4"
            >
              <VSelect
                v-model="templateForm.defaultCurrency"
                :items="['USD', 'EUR', 'GBP', 'CAD', 'AUD']"
                label="Default Currency"
                prepend-inner-icon="tabler-currency"
              />
            </VCol>

            <!-- Services Section -->
            <VCol cols="12">
              <VLabel class="mb-2">
                Template Services
              </VLabel>

              <!-- Add New Service -->
              <VCard
                variant="outlined"
                class="mb-4"
              >
                <VCardTitle class="text-subtitle-1">
                  Add Service Template
                </VCardTitle>
                <VCardText>
                  <VRow>
                    <VCol
                      cols="12"
                      md="6"
                    >
                      <AppTextField
                        v-model="newService.serviceName"
                        label="Service Name"
                        placeholder="e.g., Portrait Session"
                        density="compact"
                      />
                    </VCol>

                    <VCol
                      cols="12"
                      md="6"
                    >
                      <AppTextField
                        v-model="newService.category"
                        label="Category"
                        placeholder="e.g., Photography"
                        density="compact"
                      />
                    </VCol>

                    <VCol cols="12">
                      <AppTextarea
                        v-model="newService.description"
                        label="Description"
                        placeholder="Describe this service template"
                        rows="2"
                        density="compact"
                      />
                    </VCol>

                    <VCol
                      cols="12"
                      md="4"
                    >
                      <AppTextField
                        v-model.number="newService.defaultRate"
                        label="Default Rate"
                        type="number"
                        density="compact"
                      />
                    </VCol>

                    <VCol
                      cols="12"
                      md="4"
                    >
                      <VSelect
                        v-model="newService.unit"
                        :items="units"
                        item-title="title"
                        item-value="value"
                        label="Unit"
                        density="compact"
                      />
                    </VCol>

                    <VCol
                      cols="12"
                      md="4"
                    >
                      <VCheckbox
                        v-model="newService.isRequired"
                        label="Required service"
                        density="compact"
                      />
                    </VCol>
                  </VRow>

                  <div class="d-flex justify-end mt-2">
                    <VBtn
                      color="primary"
                      size="small"
                      @click="addService"
                    >
                      Add Service
                    </VBtn>
                  </div>
                </VCardText>
              </VCard>

              <!-- Existing Services -->
              <div v-if="templateForm.services.length > 0">
                <VCard
                  v-for="(service, index) in templateForm.services"
                  :key="index"
                  variant="outlined"
                  class="mb-2"
                >
                  <VCardText class="py-2">
                    <div class="d-flex align-center justify-space-between">
                      <div>
                        <div class="font-weight-medium">
                          {{ service.serviceName }}
                          <VChip
                            v-if="service.isRequired"
                            color="warning"
                            size="x-small"
                            variant="tonal"
                            class="ms-2"
                          >
                            Required
                          </VChip>
                        </div>
                        <div class="text-caption text-medium-emphasis">
                          Default: {{ service.defaultRate }} {{ templateForm.defaultCurrency }} {{ service.unit }}
                          <span v-if="service.category"> • {{ service.category }}</span>
                        </div>
                        <div
                          v-if="service.description"
                          class="text-caption"
                        >
                          {{ service.description }}
                        </div>
                      </div>
                      <VBtn
                        icon
                        size="small"
                        color="error"
                        variant="text"
                        @click="removeService(index)"
                      >
                        <VIcon icon="tabler-trash" />
                      </VBtn>
                    </div>
                  </VCardText>
                </VCard>
              </div>
            </VCol>

            <VCol cols="12">
              <VLabel class="mb-2">
                Tags
              </VLabel>
              <div class="d-flex gap-2 mb-2">
                <AppTextField
                  v-model="tagInput"
                  placeholder="Add a tag"
                  density="compact"
                  @keypress="handleTagKeypress"
                />
                <VBtn
                  color="primary"
                  variant="outlined"
                  size="small"
                  @click="addTag"
                >
                  Add
                </VBtn>
              </div>
              <div class="d-flex flex-wrap gap-2">
                <VChip
                  v-for="(tag, index) in templateForm.tags"
                  :key="index"
                  size="small"
                  closable
                  @click:close="removeTag(index)"
                >
                  {{ tag }}
                </VChip>
              </div>
            </VCol>

            <VCol cols="12">
              <VRow>
                <VCol
                  cols="12"
                  md="6"
                >
                  <VCheckbox
                    v-model="templateForm.isActive"
                    label="Active template"
                    density="compact"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <VCheckbox
                    v-model="templateForm.isDefault"
                    label="Set as default template"
                    density="compact"
                  />
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="outlined"
          @click="handleDialogClose"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          Create Template
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
