<script setup>
import { computed } from 'vue'

const props = defineProps({
  checklist: {
    type: Object,
    required: true,
  },
  viewMode: {
    type: String,
    default: 'grid',
  },
})

const emit = defineEmits(['view', 'edit', 'delete'])

// Computed
const completionPercentage = computed(() => {
  return props.checklist.completionPercentage || 0
})

const completedItems = computed(() => {
  return props.checklist.completedCount || 0
})

const totalItems = computed(() => {
  return props.checklist.totalCount || 0
})

const progressColor = computed(() => {
  const percentage = completionPercentage.value
  if (percentage === 100) return 'success'
  if (percentage >= 75) return 'info'
  if (percentage >= 50) return 'warning'
  if (percentage >= 25) return 'orange'
  
  return 'error'
})

const statusText = computed(() => {
  const percentage = completionPercentage.value
  if (percentage === 100) return 'Completed'
  if (percentage === 0) return 'Not Started'
  
  return 'In Progress'
})

const statusColor = computed(() => {
  const percentage = completionPercentage.value
  if (percentage === 100) return 'success'
  if (percentage === 0) return 'default'
  
  return 'primary'
})

const highPriorityItems = computed(() => {
  if (!props.checklist.items) return 0
  
  return props.checklist.items.filter(item => item.priority === 'high' && !item.completed).length
})

const overdueItems = computed(() => {
  if (!props.checklist.items) return 0
  const now = new Date()
  
  return props.checklist.items.filter(item => {
    return item.dueDate && new Date(item.dueDate) < now && !item.completed
  }).length
})

const formatDate = dateString => {
  if (!dateString) return ''
  
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
}

// Methods
const handleView = () => {
  emit('view', props.checklist._id)
}

const handleEdit = () => {
  emit('edit', props.checklist._id, {
    title: props.checklist.title,
    description: props.checklist.description,
    color: props.checklist.color,
  })
}

const handleDelete = () => {
  emit('delete', props.checklist._id)
}
</script>

<template>
  <VCard
    :class="{ 'list-view': viewMode === 'list' }"
    hover
    style="cursor: pointer;"
    @click="handleView"
  >
    <!-- Color indicator -->
    <div
      class="color-indicator"
      :style="{ backgroundColor: checklist.color }"
    />

    <VCardText>
      <div :class="viewMode === 'list' ? 'd-flex align-center' : ''">
        <!-- Main content -->
        <div :class="viewMode === 'list' ? 'flex-grow-1' : ''">
          <!-- Header -->
          <div class="d-flex align-center justify-space-between mb-3">
            <VChip
              :color="statusColor"
              size="small"
              variant="tonal"
            >
              {{ statusText }}
            </VChip>
            
            <VMenu>
              <template #activator="{ props: menuProps }">
                <VBtn
                  v-bind="menuProps"
                  icon="tabler-dots-vertical"
                  variant="text"
                  size="small"
                  @click.stop
                />
              </template>
              <VList>
                <VListItem @click.stop="handleView">
                  <template #prepend>
                    <VIcon icon="tabler-eye" />
                  </template>
                  <VListItemTitle>View</VListItemTitle>
                </VListItem>
                <VListItem @click.stop="handleEdit">
                  <template #prepend>
                    <VIcon icon="tabler-edit" />
                  </template>
                  <VListItemTitle>Edit</VListItemTitle>
                </VListItem>
                <VDivider />
                <VListItem
                  class="text-error"
                  @click.stop="handleDelete"
                >
                  <template #prepend>
                    <VIcon icon="tabler-trash" />
                  </template>
                  <VListItemTitle>Delete</VListItemTitle>
                </VListItem>
              </VList>
            </VMenu>
          </div>

          <!-- Title and Description -->
          <h3 class="text-h6 font-weight-bold mb-2 line-clamp-2">
            {{ checklist.title }}
          </h3>
          <p 
            v-if="checklist.description" 
            class="text-body-2 text-medium-emphasis mb-4 line-clamp-2"
          >
            {{ checklist.description }}
          </p>

          <!-- Progress -->
          <div class="mb-4">
            <div class="d-flex align-center justify-space-between mb-2">
              <span class="text-body-2 font-weight-medium">
                Progress
              </span>
              <span class="text-body-2">
                {{ completedItems }}/{{ totalItems }} tasks
              </span>
            </div>
            <VProgressLinear
              :model-value="completionPercentage"
              :color="progressColor"
              height="8"
              rounded
            />
            <div class="text-center mt-1">
              <span class="text-caption font-weight-medium">
                {{ completionPercentage }}% Complete
              </span>
            </div>
          </div>

          <!-- Alerts -->
          <div
            v-if="highPriorityItems > 0 || overdueItems > 0"
            class="mb-4"
          >
            <VChip
              v-if="highPriorityItems > 0"
              color="error"
              size="small"
              variant="tonal"
              class="me-2 mb-1"
            >
              <VIcon
                icon="tabler-alert-triangle"
                size="14"
                class="me-1"
              />
              {{ highPriorityItems }} high priority
            </VChip>
            <VChip
              v-if="overdueItems > 0"
              color="warning"
              size="small"
              variant="tonal"
              class="mb-1"
            >
              <VIcon
                icon="tabler-clock-exclamation"
                size="14"
                class="me-1"
              />
              {{ overdueItems }} overdue
            </VChip>
          </div>
        </div>

        <!-- List view additional info -->
        <div
          v-if="viewMode === 'list'"
          class="ms-4 text-end"
        >
          <div class="text-body-2 text-medium-emphasis mb-1">
            Created
          </div>
          <div class="text-body-2 font-weight-medium mb-3">
            {{ formatDate(checklist.createdAt) }}
          </div>
          
          <div class="text-body-2 text-medium-emphasis mb-1">
            Last Updated
          </div>
          <div class="text-body-2 font-weight-medium">
            {{ formatDate(checklist.updatedAt) }}
          </div>
        </div>
      </div>

      <!-- Footer (Grid view only) -->
      <div
        v-if="viewMode === 'grid'"
        class="d-flex align-center justify-space-between"
      >
        <div class="text-caption text-medium-emphasis">
          Created {{ formatDate(checklist.createdAt) }}
        </div>
        <VBtn
          color="primary"
          variant="text"
          size="small"
          append-icon="tabler-arrow-right"
          @click.stop="handleView"
        >
          View Details
        </VBtn>
      </div>
    </VCardText>
  </VCard>
</template>

<style scoped>
.color-indicator {
  height: 4px;
  width: 100%;
}

.list-view {
  margin-bottom: 1rem;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.v-card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}
</style>
