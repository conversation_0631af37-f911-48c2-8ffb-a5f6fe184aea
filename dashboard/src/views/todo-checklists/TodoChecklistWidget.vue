<script setup>
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketTodoChecklistStore } from '@stores/todo-checklists'
import { socket } from '@socket/socket'

const storeAuth = useSocketStore()
const todoChecklistStore = useSocketTodoChecklistStore()

const { user } = storeToRefs(storeAuth)
const { todoChecklists } = storeToRefs(todoChecklistStore)

const loading = ref(false)

// Computed
const recentChecklists = computed(() => {
  if (!todoChecklists.value) return []
  
  return todoChecklists.value
    .slice()
    .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
    .slice(0, 5)
})

const totalTasks = computed(() => {
  if (!todoChecklists.value) return 0
  
  return todoChecklists.value.reduce((total, checklist) => total + (checklist.totalCount || 0), 0)
})

const completedTasks = computed(() => {
  if (!todoChecklists.value) return 0
  
  return todoChecklists.value.reduce((total, checklist) => total + (checklist.completedCount || 0), 0)
})

const overallProgress = computed(() => {
  if (totalTasks.value === 0) return 0
  
  return Math.round((completedTasks.value / totalTasks.value) * 100)
})

const urgentTasks = computed(() => {
  if (!todoChecklists.value) return 0
  let urgent = 0
  todoChecklists.value.forEach(checklist => {
    if (checklist.items) {
      urgent += checklist.items.filter(item => 
        !item.completed && 
        (item.priority === 'high' || (item.dueDate && new Date(item.dueDate) < new Date())),
      ).length
    }
  })
  
  return urgent
})

// Socket listeners
socket.on('allTodoChecklists', data => {
  if (data.status === 'success') {
    todoChecklists.value = data.data
    loading.value = false
  }
})

// Methods
const formatDate = dateString => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
  })
}

const getProgressColor = percentage => {
  if (percentage === 100) return 'success'
  if (percentage >= 75) return 'info'
  if (percentage >= 50) return 'warning'
  
  return 'error'
}

// Lifecycle
onMounted(() => {
  if (!todoChecklists.value) {
    loading.value = true
    todoChecklistStore.getAllTodoChecklists({
      user: user.value._id,
    })
  }
})
</script>

<template>
  <VCard>
    <VCardTitle class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <VIcon
          icon="tabler-checklist"
          class="me-2"
        />
        Todo Checklists
      </div>
      <VBtn
        variant="text"
        size="small"
        to="/business-tools/todo-checklists"
      >
        View All
      </VBtn>
    </VCardTitle>

    <VDivider />

    <!-- Loading State -->
    <div
      v-if="loading"
      class="text-center py-8"
    >
      <VProgressCircular
        indeterminate
        color="primary"
        size="32"
      />
    </div>

    <!-- Content -->
    <div v-else>
      <!-- Stats Overview -->
      <VCardText class="pb-0">
        <VRow>
          <VCol cols="6">
            <div class="text-center">
              <h3 class="text-h4 font-weight-bold">
                {{ totalTasks }}
              </h3>
              <p class="text-body-2 mb-0">
                Total Tasks
              </p>
            </div>
          </VCol>
          <VCol cols="6">
            <div class="text-center">
              <h3 class="text-h4 font-weight-bold text-success">
                {{ completedTasks }}
              </h3>
              <p class="text-body-2 mb-0">
                Completed
              </p>
            </div>
          </VCol>
        </VRow>

        <!-- Overall Progress -->
        <div class="mt-4">
          <div class="d-flex align-center justify-space-between mb-2">
            <span class="text-body-2 font-weight-medium">Overall Progress</span>
            <span class="text-body-2">{{ overallProgress }}%</span>
          </div>
          <VProgressLinear
            :model-value="overallProgress"
            :color="getProgressColor(overallProgress)"
            height="6"
            rounded
          />
        </div>

        <!-- Urgent Tasks Alert -->
        <VAlert
          v-if="urgentTasks > 0"
          type="warning"
          variant="tonal"
          class="mt-4"
          density="compact"
        >
          <VIcon
            icon="tabler-alert-triangle"
            class="me-2"
          />
          {{ urgentTasks }} urgent task{{ urgentTasks > 1 ? 's' : '' }} need attention
        </VAlert>
      </VCardText>

      <!-- Recent Checklists -->
      <VCardText v-if="recentChecklists.length > 0">
        <h4 class="text-h6 mb-3">
          Recent Checklists
        </h4>
        <div class="space-y-3">
          <div
            v-for="checklist in recentChecklists"
            :key="checklist._id"
            class="d-flex align-center py-2"
          >
            <div
              class="color-indicator me-3"
              :style="{ backgroundColor: checklist.color }"
            />
            <div class="flex-grow-1">
              <h5 class="text-body-1 font-weight-medium line-clamp-1">
                {{ checklist.title }}
              </h5>
              <div class="d-flex align-center">
                <span class="text-caption text-medium-emphasis me-2">
                  {{ checklist.completedCount || 0 }}/{{ checklist.totalCount || 0 }} tasks
                </span>
                <VChip
                  :color="getProgressColor(checklist.completionPercentage || 0)"
                  size="x-small"
                  variant="tonal"
                >
                  {{ checklist.completionPercentage || 0 }}%
                </VChip>
              </div>
            </div>
            <div class="text-end">
              <span class="text-caption text-medium-emphasis">
                {{ formatDate(checklist.updatedAt) }}
              </span>
            </div>
          </div>
        </div>
      </VCardText>

      <!-- Empty State -->
      <VCardText
        v-else
        class="text-center py-8"
      >
        <VIcon
          icon="tabler-checklist"
          size="48"
          color="disabled"
          class="mb-3"
        />
        <h4 class="text-h6 mb-2">
          No checklists yet
        </h4>
        <p class="text-body-2 mb-3">
          Create your first todo checklist to get started
        </p>
        <VBtn
          color="primary"
          size="small"
          to="/business-tools/todo-checklists"
        >
          Create Checklist
        </VBtn>
      </VCardText>
    </div>
  </VCard>
</template>

<style scoped>
.color-indicator {
  width: 4px;
  height: 32px;
  border-radius: 2px;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}
</style>
