<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'create'])

// Form data
const formData = ref({
  title: '',
  description: '',
  color: '#C5A2FF', // Default purple color
})

// Form validation
const titleRules = [
  v => !!v || 'Title is required',
  v => (v && v.length >= 3) || 'Title must be at least 3 characters',
  v => (v && v.length <= 100) || 'Title must be less than 100 characters',
]

// Color options with user's preferred palette
const colorOptions = [
  { name: 'Purple', value: '#C5A2FF' },
  { name: 'Blue', value: '#4CF3EA' },
  { name: 'Yellow', value: '#FED56A' },
  { name: 'Green', value: '#76FFCE' },
  { name: 'Red', value: '#E57B7B' },
  { name: 'Pink', value: '#F51BC5' },
]

// Computed
const isOpen = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// Methods
const resetForm = () => {
  formData.value = {
    title: '',
    description: '',
    color: '#C5A2FF',
  }
}

const handleCreate = () => {
  if (formData.value.title.trim()) {
    emit('create', {
      title: formData.value.title.trim(),
      description: formData.value.description.trim(),
      color: formData.value.color,
    })
    resetForm()
  }
}

const handleCancel = () => {
  isOpen.value = false
  resetForm()
}

// Watch for dialog close to reset form
watch(isOpen, newValue => {
  if (!newValue) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    v-model="isOpen"
    max-width="600"
    persistent
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span class="text-h5">Create Todo Checklist</span>
        <VBtn
          icon="tabler-x"
          variant="text"
          size="small"
          @click="handleCancel"
        />
      </VCardTitle>

      <VDivider />

      <VCardText class="pt-6">
        <VForm @submit.prevent="handleCreate">
          <VRow>
            <!-- Title -->
            <VCol cols="12">
              <VTextField
                v-model="formData.title"
                label="Checklist Title"
                placeholder="Enter checklist title..."
                variant="outlined"
                :rules="titleRules"
                required
                autofocus
              />
            </VCol>

            <!-- Description -->
            <VCol cols="12">
              <VTextarea
                v-model="formData.description"
                label="Description (Optional)"
                placeholder="Enter checklist description..."
                variant="outlined"
                rows="3"
                no-resize
              />
            </VCol>

            <!-- Color Selection -->
            <VCol cols="12">
              <VLabel class="text-body-2 font-weight-medium mb-3">
                Choose Color
              </VLabel>
              <div class="d-flex flex-wrap gap-3">
                <VTooltip
                  v-for="color in colorOptions"
                  :key="color.value"
                  :text="color.name"
                  location="top"
                >
                  <template #activator="{ props: tooltipProps }">
                    <VBtn
                      v-bind="tooltipProps"
                      :color="color.value"
                      :variant="formData.color === color.value ? 'elevated' : 'tonal'"
                      size="large"
                      icon
                      class="color-picker-btn"
                      :class="{ 'selected': formData.color === color.value }"
                      @click="formData.color = color.value"
                    >
                      <VIcon
                        v-if="formData.color === color.value"
                        icon="tabler-check"
                        color="white"
                      />
                    </VBtn>
                  </template>
                </VTooltip>
              </div>
            </VCol>

            <!-- Preview -->
            <VCol cols="12">
              <VLabel class="text-body-2 font-weight-medium mb-3">
                Preview
              </VLabel>
              <VCard
                :style="{ borderLeft: `4px solid ${formData.color}` }"
                variant="outlined"
              >
                <VCardText>
                  <h4 class="text-h6 mb-2">
                    {{ formData.title || 'Checklist Title' }}
                  </h4>
                  <p class="text-body-2 mb-0">
                    {{ formData.description || 'Checklist description will appear here...' }}
                  </p>
                  <VChip
                    :color="formData.color"
                    size="small"
                    class="mt-2"
                  >
                    0% Complete
                  </VChip>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions class="pa-4">
        <VSpacer />
        <VBtn
          variant="outlined"
          @click="handleCancel"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!formData.title.trim()"
          @click="handleCreate"
        >
          Create Checklist
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style scoped>
.color-picker-btn {
  transition: all 0.2s ease;
}

.color-picker-btn.selected {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
