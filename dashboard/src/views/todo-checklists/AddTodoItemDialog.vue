<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'add'])

// Form data
const formData = ref({
  title: '',
  description: '',
  priority: 'medium',
  dueDate: null,
  tags: [],
})

// Form validation
const titleRules = [
  v => !!v || 'Title is required',
  v => (v && v.length >= 2) || 'Title must be at least 2 characters',
  v => (v && v.length <= 200) || 'Title must be less than 200 characters',
]

// Priority options
const priorityOptions = [
  { title: 'High Priority', value: 'high', color: 'error', icon: 'tabler-alert-triangle' },
  { title: 'Medium Priority', value: 'medium', color: 'warning', icon: 'tabler-clock' },
  { title: 'Low Priority', value: 'low', color: 'success', icon: 'tabler-check' },
]

// Tag input
const newTag = ref('')

// Computed
const isOpen = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

const selectedPriorityOption = computed(() => {
  return priorityOptions.find(option => option.value === formData.value.priority)
})

// Methods
const resetForm = () => {
  formData.value = {
    title: '',
    description: '',
    priority: 'medium',
    dueDate: null,
    tags: [],
  }
  newTag.value = ''
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !formData.value.tags.includes(tag)) {
    formData.value.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = index => {
  formData.value.tags.splice(index, 1)
}

const handleAdd = () => {
  if (formData.value.title.trim()) {
    const itemData = {
      title: formData.value.title.trim(),
      description: formData.value.description.trim(),
      priority: formData.value.priority,
      dueDate: formData.value.dueDate,
      tags: formData.value.tags,
    }

    emit('add', itemData)
    resetForm()
  }
}

const handleCancel = () => {
  isOpen.value = false
  resetForm()
}

// Watch for dialog close to reset form
watch(isOpen, newValue => {
  if (!newValue) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    v-model="isOpen"
    max-width="600"
    persistent
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span class="text-h5">Add New Task</span>
        <VBtn
          icon="tabler-x"
          variant="text"
          size="small"
          @click="handleCancel"
        />
      </VCardTitle>

      <VDivider />

      <VCardText class="pt-6">
        <VForm @submit.prevent="handleAdd">
          <VRow>
            <!-- Title -->
            <VCol cols="12">
              <VTextField
                v-model="formData.title"
                label="Task Title"
                placeholder="Enter task title..."
                variant="outlined"
                :rules="titleRules"
                required
                autofocus
              />
            </VCol>

            <!-- Description -->
            <VCol cols="12">
              <VTextarea
                v-model="formData.description"
                label="Description (Optional)"
                placeholder="Enter task description..."
                variant="outlined"
                rows="3"
                no-resize
              />
            </VCol>

            <!-- Priority -->
            <VCol
              cols="12"
              md="6"
            >
              <VLabel class="text-body-2 font-weight-medium mb-3">
                Priority Level
              </VLabel>
              <VRadioGroup
                v-model="formData.priority"
                inline
                hide-details
              >
                <VRadio
                  v-for="option in priorityOptions"
                  :key="option.value"
                  :value="option.value"
                  :color="option.color"
                >
                  <template #label>
                    <div class="d-flex align-center">
                      <VIcon
                        :icon="option.icon"
                        :color="option.color"
                        size="16"
                        class="me-2"
                      />
                      <span>{{ option.title.replace(' Priority', '') }}</span>
                    </div>
                  </template>
                </VRadio>
              </VRadioGroup>
            </VCol>

            <!-- Due Date -->
            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model="formData.dueDate"
                label="Due Date (Optional)"
                type="date"
                variant="outlined"
                hide-details
              />
            </VCol>

            <!-- Tags -->
            <VCol cols="12">
              <VLabel class="text-body-2 font-weight-medium mb-3">
                Tags (Optional)
              </VLabel>
              
              <!-- Add Tag Input -->
              <div class="d-flex align-center mb-3">
                <VTextField
                  v-model="newTag"
                  placeholder="Add a tag..."
                  variant="outlined"
                  density="compact"
                  hide-details
                  class="flex-grow-1 me-2"
                  @keyup.enter="addTag"
                />
                <VBtn
                  color="primary"
                  variant="outlined"
                  size="small"
                  :disabled="!newTag.trim()"
                  @click="addTag"
                >
                  Add
                </VBtn>
              </div>

              <!-- Tags Display -->
              <div
                v-if="formData.tags.length > 0"
                class="d-flex flex-wrap gap-2"
              >
                <VChip
                  v-for="(tag, index) in formData.tags"
                  :key="index"
                  size="small"
                  closable
                  @click:close="removeTag(index)"
                >
                  {{ tag }}
                </VChip>
              </div>
            </VCol>

            <!-- Preview -->
            <VCol cols="12">
              <VLabel class="text-body-2 font-weight-medium mb-3">
                Preview
              </VLabel>
              <VCard variant="outlined">
                <VCardText>
                  <div class="d-flex align-center mb-2">
                    <VCheckbox
                      :model-value="false"
                      disabled
                      hide-details
                      density="compact"
                    />
                    <div class="flex-grow-1 ms-3">
                      <h4 class="text-h6">
                        {{ formData.title || 'Task Title' }}
                      </h4>
                      <p
                        v-if="formData.description"
                        class="text-body-2 mb-0"
                      >
                        {{ formData.description }}
                      </p>
                    </div>
                    <VChip
                      :color="selectedPriorityOption?.color"
                      size="small"
                      variant="tonal"
                    >
                      <VIcon
                        :icon="selectedPriorityOption?.icon"
                        size="14"
                        class="me-1"
                      />
                      {{ formData.priority }}
                    </VChip>
                  </div>
                  
                  <div
                    v-if="formData.dueDate || formData.tags.length > 0"
                    class="d-flex align-center flex-wrap gap-2"
                  >
                    <VChip
                      v-if="formData.dueDate"
                      size="small"
                      variant="outlined"
                      prepend-icon="tabler-calendar"
                    >
                      {{ new Date(formData.dueDate).toLocaleDateString() }}
                    </VChip>
                    <VChip
                      v-for="tag in formData.tags"
                      :key="tag"
                      size="small"
                      variant="outlined"
                    >
                      {{ tag }}
                    </VChip>
                  </div>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions class="pa-4">
        <VSpacer />
        <VBtn
          variant="outlined"
          @click="handleCancel"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!formData.title.trim()"
          @click="handleAdd"
        >
          Add Task
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style scoped>
.v-radio :deep(.v-label) {
  opacity: 1;
}
</style>
