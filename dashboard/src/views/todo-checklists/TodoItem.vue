<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  checklistColor: {
    type: String,
    default: '#C5A2FF',
  },
  isLast: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['toggle', 'update', 'delete'])

// State
const isEditing = ref(false)

const editForm = ref({
  title: '',
  description: '',
  priority: 'medium',
  dueDate: null,
  tags: [],
})

// Computed
const priorityColor = computed(() => {
  switch (props.item.priority) {
  case 'high': return 'error'
  case 'medium': return 'warning'
  case 'low': return 'success'
  default: return 'default'
  }
})

const priorityIcon = computed(() => {
  switch (props.item.priority) {
  case 'high': return 'tabler-alert-triangle'
  case 'medium': return 'tabler-clock'
  case 'low': return 'tabler-check'
  default: return 'tabler-minus'
  }
})

const isOverdue = computed(() => {
  if (!props.item.dueDate || props.item.completed) return false
  
  return new Date(props.item.dueDate) < new Date()
})

const dueDateFormatted = computed(() => {
  if (!props.item.dueDate) return null
  
  return new Date(props.item.dueDate).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
})

const createdDateFormatted = computed(() => {
  return new Date(props.item.createdAt).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
})

// Methods
const handleToggle = () => {
  emit('toggle', props.item._id)
}

const startEdit = () => {
  editForm.value = {
    title: props.item.title,
    description: props.item.description || '',
    priority: props.item.priority,
    dueDate: props.item.dueDate ? props.item.dueDate.split('T')[0] : null,
    tags: [...(props.item.tags || [])],
  }
  isEditing.value = true
}

const saveEdit = () => {
  if (editForm.value.title.trim()) {
    emit('update', props.item._id, {
      title: editForm.value.title.trim(),
      description: editForm.value.description.trim(),
      priority: editForm.value.priority,
      dueDate: editForm.value.dueDate,
      tags: editForm.value.tags,
      completed: props.item.completed, // Preserve completion status
    })
    isEditing.value = false
  }
}

const cancelEdit = () => {
  isEditing.value = false
}

const handleDelete = () => {
  if (confirm('Are you sure you want to delete this task?')) {
    emit('delete', props.item._id)
  }
}

const addTag = tag => {
  if (tag && !editForm.value.tags.includes(tag)) {
    editForm.value.tags.push(tag)
  }
}

const removeTag = index => {
  editForm.value.tags.splice(index, 1)
}
</script>

<template>
  <VCardText class="py-4">
    <!-- Edit Mode -->
    <div v-if="isEditing">
      <VRow>
        <VCol cols="12">
          <VTextField
            v-model="editForm.title"
            label="Task Title"
            variant="outlined"
            density="compact"
            required
          />
        </VCol>
        <VCol cols="12">
          <VTextarea
            v-model="editForm.description"
            label="Description"
            variant="outlined"
            density="compact"
            rows="2"
            no-resize
          />
        </VCol>
        <VCol
          cols="12"
          md="4"
        >
          <VSelect
            v-model="editForm.priority"
            :items="[
              { title: 'High', value: 'high' },
              { title: 'Medium', value: 'medium' },
              { title: 'Low', value: 'low' }
            ]"
            label="Priority"
            variant="outlined"
            density="compact"
          />
        </VCol>
        <VCol
          cols="12"
          md="4"
        >
          <VTextField
            v-model="editForm.dueDate"
            label="Due Date"
            type="date"
            variant="outlined"
            density="compact"
          />
        </VCol>
        <VCol
          cols="12"
          md="4"
        >
          <div class="d-flex gap-2">
            <VBtn
              color="primary"
              size="small"
              @click="saveEdit"
            >
              Save
            </VBtn>
            <VBtn
              variant="outlined"
              size="small"
              @click="cancelEdit"
            >
              Cancel
            </VBtn>
          </div>
        </VCol>
      </VRow>
    </div>

    <!-- View Mode -->
    <div v-else>
      <div class="d-flex align-center">
        <!-- Checkbox -->
        <VCheckbox
          :model-value="item.completed"
          :color="checklistColor"
          hide-details
          density="compact"
          @update:model-value="handleToggle"
        />

        <!-- Content -->
        <div class="flex-grow-1 ms-3">
          <div class="d-flex align-center justify-space-between mb-1">
            <h4 
              class="text-h6"
              :class="{ 
                'text-decoration-line-through text-medium-emphasis': item.completed,
                'font-weight-bold': !item.completed 
              }"
            >
              {{ item.title }}
            </h4>
            
            <!-- Actions Menu -->
            <VMenu>
              <template #activator="{ props: menuProps }">
                <VBtn
                  v-bind="menuProps"
                  icon="tabler-dots-vertical"
                  variant="text"
                  size="small"
                />
              </template>
              <VList>
                <VListItem @click="startEdit">
                  <template #prepend>
                    <VIcon icon="tabler-edit" />
                  </template>
                  <VListItemTitle>Edit</VListItemTitle>
                </VListItem>
                <VDivider />
                <VListItem
                  class="text-error"
                  @click="handleDelete"
                >
                  <template #prepend>
                    <VIcon icon="tabler-trash" />
                  </template>
                  <VListItemTitle>Delete</VListItemTitle>
                </VListItem>
              </VList>
            </VMenu>
          </div>

          <!-- Description -->
          <p 
            v-if="item.description" 
            class="text-body-2 mb-2"
            :class="{ 'text-medium-emphasis': item.completed }"
          >
            {{ item.description }}
          </p>

          <!-- Meta Information -->
          <div class="d-flex align-center flex-wrap gap-2">
            <!-- Priority -->
            <VChip
              :color="priorityColor"
              size="small"
              variant="tonal"
            >
              <VIcon
                :icon="priorityIcon"
                size="14"
                class="me-1"
              />
              {{ item.priority }}
            </VChip>

            <!-- Due Date -->
            <VChip
              v-if="item.dueDate"
              :color="isOverdue ? 'error' : 'default'"
              :variant="isOverdue ? 'flat' : 'outlined'"
              size="small"
              prepend-icon="tabler-calendar"
            >
              <VIcon
                v-if="isOverdue"
                icon="tabler-alert-triangle"
                size="14"
                class="me-1"
              />
              {{ dueDateFormatted }}
              <span
                v-if="isOverdue"
                class="ms-1"
              >(Overdue)</span>
            </VChip>

            <!-- Tags -->
            <VChip
              v-for="tag in item.tags"
              :key="tag"
              size="small"
              variant="outlined"
              color="primary"
            >
              {{ tag }}
            </VChip>

            <!-- Created Date -->
            <VChip
              size="small"
              variant="text"
              class="text-caption"
            >
              Created {{ createdDateFormatted }}
            </VChip>
          </div>
        </div>
      </div>
    </div>

    <!-- Divider -->
    <VDivider
      v-if="!isLast"
      class="mt-4"
    />
  </VCardText>
</template>

<style scoped>
.v-card-text {
  transition: background-color 0.2s ease;
}

.v-card-text:hover {
  background-color: rgba(var(--v-theme-on-surface), 0.02);
}
</style>
