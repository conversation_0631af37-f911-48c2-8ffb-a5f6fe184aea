<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketCollaboratorStore } from '@stores/collaborators'

const propsS = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible } = toRefs(propsS)

const storeAuth = useSocketStore()
const collaboratorStore = useSocketCollaboratorStore()

const { user } = storeToRefs(storeAuth)

// Form data
const collaboratorForm = ref({
  user: '',
  job: '',
  role: 'viewer',
})

// Mock data - In real app, these would come from API
const users = ref([
  { _id: '1', name: '<PERSON>', email: '<EMAIL>' },
  { _id: '2', name: '<PERSON>', email: '<EMAIL>' },
  { _id: '3', name: '<PERSON>', email: '<EMAIL>' },
])

const jobs = ref([
  { _id: '1', title: 'Website Redesign' },
  { _id: '2', title: 'Mobile App Development' },
  { _id: '3', title: 'Brand Identity' },
])

// Role options
const roleOptions = [
  { title: 'Viewer', value: 'viewer', description: 'Can view project details' },
  { title: 'Editor', value: 'editor', description: 'Can edit project content' },
  { title: 'Admin', value: 'admin', description: 'Full project access' },
]

// Form validation
const isFormValid = computed(() => {
  return collaboratorForm.value.user && 
         collaboratorForm.value.job && 
         collaboratorForm.value.role
})

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  collaboratorStore.createCollaborator({
    user: user.value._id,
    collaborator: collaboratorForm.value,
  })

  handleDialogClose()
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  collaboratorForm.value = {
    user: '',
    job: '',
    role: 'viewer',
  }
}

// Watch dialog close to reset form
watch(isDialogVisible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="600"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Add Collaborator</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider class="mt-5 mb-5" />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <AppSelect
                v-model="collaboratorForm.user"
                label="Select User"
                placeholder="Choose a user to add as collaborator"
                :items="users"
                item-title="name"
                item-value="_id"
                :rules="[v => !!v || 'User is required']"
              >
                <template #item="{ props, item }">
                  <VListItem v-bind="props">
                    <template #prepend>
                      <VAvatar size="32">
                        <span>{{ item.raw.name.charAt(0) }}</span>
                      </VAvatar>
                    </template>
                    <VListItemTitle>{{ item.raw.name }}</VListItemTitle>
                    <VListItemSubtitle>{{ item.raw.email }}</VListItemSubtitle>
                  </VListItem>
                </template>
              </AppSelect>
            </VCol>

            <VCol cols="12">
              <AppSelect
                v-model="collaboratorForm.job"
                label="Select Job/Project"
                placeholder="Choose a project for this collaborator"
                :items="jobs"
                item-title="title"
                item-value="_id"
                :rules="[v => !!v || 'Job is required']"
              />
            </VCol>

            <VCol cols="12">
              <VLabel class="mb-2">
                Role & Permissions
              </VLabel>
              <VRadioGroup
                v-model="collaboratorForm.role"
                inline
              >
                <VRadio
                  v-for="role in roleOptions"
                  :key="role.value"
                  :label="role.title"
                  :value="role.value"
                />
              </VRadioGroup>
              
              <!-- Role descriptions -->
              <VCard
                v-for="role in roleOptions"
                v-show="collaboratorForm.role === role.value"
                :key="role.value"
                variant="tonal"
                class="mt-3"
              >
                <VCardText class="py-3">
                  <div class="d-flex align-center">
                    <VIcon
                      :icon="role.value === 'admin' ? 'tabler-crown' : 
                        role.value === 'editor' ? 'tabler-edit' : 'tabler-eye'"
                      class="me-2"
                    />
                    <span class="font-weight-medium">{{ role.title }}</span>
                  </div>
                  <div class="text-caption mt-1">
                    {{ role.description }}
                  </div>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider class="mt-5 mb-5" />

      <VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            color="secondary"
            variant="outlined"
            @click="handleDialogClose"
          >
            Cancel
          </VBtn>
          <VBtn
            variant="outlined"
            color="primary"
            :disabled="!isFormValid"
            @click="handleSubmit"
          >
            Add Collaborator
          </VBtn>
        </VCardActions>
      </VCardText>
    </VCard>
  </VDialog>
</template>
