<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketCollaboratorStore } from '@stores/collaborators'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  collaborator: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible, collaborator } = toRefs(props)

const storeAuth = useSocketStore()
const collaboratorStore = useSocketCollaboratorStore()

const { user } = storeToRefs(storeAuth)

// Form data
const collaboratorForm = ref({
  id: '',
  role: 'viewer',
  status: 'active',
})

// Role options
const roleOptions = [
  { title: 'Viewer', value: 'viewer', description: 'Can view project details' },
  { title: 'Editor', value: 'editor', description: 'Can edit project content' },
  { title: 'Admin', value: 'admin', description: 'Full project access' },
]

// Status options
const statusOptions = [
  { title: 'Active', value: 'active' },
  { title: 'Inactive', value: 'inactive' },
  { title: 'Suspended', value: 'suspended' },
]

// Watch for collaborator changes
watch(collaborator, newCollaborator => {
  if (newCollaborator) {
    collaboratorForm.value = {
      id: newCollaborator._id,
      role: newCollaborator.role,
      status: newCollaborator.status,
    }
  }
}, { deep: true, immediate: true })

// Form validation
const isFormValid = computed(() => {
  return collaboratorForm.value.id && 
         collaboratorForm.value.role && 
         collaboratorForm.value.status
})

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  collaboratorStore.updateCollaborator({
    user: user.value._id,
    collaborator: collaboratorForm.value,
  })

  handleDialogClose()
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  collaboratorForm.value = {
    id: '',
    role: 'viewer',
    status: 'active',
  }
}

// Watch dialog close to reset form
watch(isDialogVisible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="600"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Edit Collaborator</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider class="mt-5 mb-5" />

      <VCardText>
        <!-- Collaborator Info -->
        <div
          v-if="collaborator"
          class="mb-6"
        >
          <div class="d-flex align-center mb-4">
            <VAvatar
              size="48"
              class="me-4"
            >
              <VImg
                v-if="collaborator.user?.avatar"
                :src="collaborator.user.avatar"
              />
              <span v-else>{{ collaborator.user?.name?.charAt(0) || 'U' }}</span>
            </VAvatar>
            <div>
              <div class="text-h6">
                {{ collaborator.user?.name || 'Unknown User' }}
              </div>
              <div class="text-body-2 text-medium-emphasis">
                {{ collaborator.user?.email }}
              </div>
              <div class="text-caption text-medium-emphasis">
                Project: {{ collaborator.job?.title || 'Unknown Project' }}
              </div>
            </div>
          </div>
        </div>

        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <VLabel class="mb-2">
                Role & Permissions
              </VLabel>
              <VRadioGroup
                v-model="collaboratorForm.role"
                inline
              >
                <VRadio
                  v-for="role in roleOptions"
                  :key="role.value"
                  :label="role.title"
                  :value="role.value"
                />
              </VRadioGroup>
              
              <!-- Role descriptions -->
              <VCard
                v-for="role in roleOptions"
                v-show="collaboratorForm.role === role.value"
                :key="role.value"
                variant="tonal"
                class="mt-3"
              >
                <VCardText class="py-3">
                  <div class="d-flex align-center">
                    <VIcon
                      :icon="role.value === 'admin' ? 'tabler-crown' : 
                        role.value === 'editor' ? 'tabler-edit' : 'tabler-eye'"
                      class="me-2"
                    />
                    <span class="font-weight-medium">{{ role.title }}</span>
                  </div>
                  <div class="text-caption mt-1">
                    {{ role.description }}
                  </div>
                </VCardText>
              </VCard>
            </VCol>

            <VCol cols="12">
              <AppSelect
                v-model="collaboratorForm.status"
                label="Status"
                :items="statusOptions"
                item-title="title"
                item-value="value"
                :rules="[v => !!v || 'Status is required']"
              />
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider class="mt-5 mb-5" />

      <VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            color="secondary"
            variant="outlined"
            @click="handleDialogClose"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            variant="outlined"
            :disabled="!isFormValid"
            @click="handleSubmit"
          >
            Update Collaborator
          </VBtn>
        </VCardActions>
      </VCardText>
    </VCard>
  </VDialog>
</template>
