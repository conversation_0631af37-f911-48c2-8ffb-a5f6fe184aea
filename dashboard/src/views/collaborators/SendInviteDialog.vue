<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketCollaboratorInviteStore } from '@stores/collaborators/invites'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible } = toRefs(props)

const storeAuth = useSocketStore()
const inviteStore = useSocketCollaboratorInviteStore()

const { user } = storeToRefs(storeAuth)

// Form data
const inviteForm = ref({
  collaboratorName: '',
  email: '',
  job: '',
  role: 'viewer',
  message: '',
  expirationDays: 7,
})

// Mock data - In real app, these would come from API
const jobs = ref([
  { _id: '1', title: 'Website Redesign' },
  { _id: '2', title: 'Mobile App Development' },
  { _id: '3', title: 'Brand Identity' },
])

// Role options
const roleOptions = [
  { title: 'Viewer', value: 'viewer', description: 'Can view project details' },
  { title: 'Editor', value: 'editor', description: 'Can edit project content' },
  { title: 'Admin', value: 'admin', description: 'Full project access' },
]

// Expiration options
const expirationOptions = [
  { title: '1 Day', value: 1 },
  { title: '3 Days', value: 3 },
  { title: '7 Days', value: 7 },
  { title: '14 Days', value: 14 },
  { title: '30 Days', value: 30 },
]

// Form validation
const isFormValid = computed(() => {
  return inviteForm.value.collaboratorName.trim() !== '' && 
         inviteForm.value.email.trim() !== '' &&
         inviteForm.value.job &&
         inviteForm.value.role &&
         isValidEmail(inviteForm.value.email)
})

// Email validation
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  
  return emailRegex.test(email)
}

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  const expirationDate = new Date()

  expirationDate.setDate(expirationDate.getDate() + inviteForm.value.expirationDays)

  const inviteData = {
    ...inviteForm.value,
    expirationDate,
  }

  inviteStore.createInvite({
    user: user.value._id,
    invite: inviteData,
  })

  handleDialogClose()
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  inviteForm.value = {
    collaboratorName: '',
    email: '',
    job: '',
    role: 'viewer',
    message: '',
    expirationDays: 7,
  }
}

// Watch dialog close to reset form
watch(isDialogVisible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="700"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Send Collaborator Invite</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="inviteForm.collaboratorName"
                label="Collaborator Name"
                placeholder="Enter full name"
                :rules="[v => !!v || 'Name is required']"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="inviteForm.email"
                label="Email Address"
                placeholder="Enter email address"
                type="email"
                :rules="[
                  v => !!v || 'Email is required',
                  v => isValidEmail(v) || 'Please enter a valid email'
                ]"
              />
            </VCol>

            <VCol cols="12">
              <AppSelect
                v-model="inviteForm.job"
                label="Select Job/Project"
                placeholder="Choose a project for this collaborator"
                :items="jobs"
                item-title="title"
                item-value="_id"
                :rules="[v => !!v || 'Job is required']"
              />
            </VCol>

            <VCol cols="12">
              <VLabel class="mb-2">
                Role & Permissions
              </VLabel>
              <VRadioGroup
                v-model="inviteForm.role"
                inline
              >
                <VRadio
                  v-for="role in roleOptions"
                  :key="role.value"
                  :label="role.title"
                  :value="role.value"
                />
              </VRadioGroup>
              
              <!-- Role descriptions -->
              <VCard
                v-for="role in roleOptions"
                v-show="inviteForm.role === role.value"
                :key="role.value"
                variant="tonal"
                class="mt-3"
              >
                <VCardText class="py-3">
                  <div class="d-flex align-center">
                    <VIcon
                      :icon="role.value === 'admin' ? 'tabler-crown' : 
                        role.value === 'editor' ? 'tabler-edit' : 'tabler-eye'"
                      class="me-2"
                    />
                    <span class="font-weight-medium">{{ role.title }}</span>
                  </div>
                  <div class="text-caption mt-1">
                    {{ role.description }}
                  </div>
                </VCardText>
              </VCard>
            </VCol>

            <VCol cols="12">
              <AppSelect
                v-model="inviteForm.expirationDays"
                label="Invite Expires In"
                :items="expirationOptions"
                item-title="title"
                item-value="value"
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="inviteForm.message"
                label="Personal Message (Optional)"
                placeholder="Add a personal message to the invitation..."
                rows="3"
                counter="500"
                :rules="[v => !v || v.length <= 500 || 'Message must be less than 500 characters']"
              />
            </VCol>
          </VRow>
        </VForm>

        <!-- Preview -->
        <VCard
          variant="tonal"
          class="mt-4"
        >
          <VCardText>
            <div class="text-subtitle-2 mb-2">
              Invite Preview:
            </div>
            <div class="text-body-2">
              <strong>{{ inviteForm.collaboratorName || 'Collaborator Name' }}</strong> 
              will be invited to join as a <strong>{{ inviteForm.role }}</strong> 
              on the <strong>{{ jobs.find(j => j._id === inviteForm.job)?.title || 'Selected Project' }}</strong> project.
            </div>
            <div class="text-caption text-medium-emphasis mt-2">
              Invite will expire in {{ inviteForm.expirationDays }} day{{ inviteForm.expirationDays !== 1 ? 's' : '' }}.
            </div>
          </VCardText>
        </VCard>
      </VCardText>

      <VDivider />

      <VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            color="secondary"
            variant="outlined"
            @click="handleDialogClose"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            variant="outlined"
            :disabled="!isFormValid"
            @click="handleSubmit"
          >
            Send Invite
          </VBtn>
        </VCardActions>
      </VCardText>
    </VCard>
  </VDialog>
</template>
