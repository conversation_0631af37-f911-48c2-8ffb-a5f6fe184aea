<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  questionnaire: {
    type: Object,
    required: true,
  },
  questions: {
    type: Array,
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  showSubmit: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['submit'])

// Form data to store answers
const answers = ref({})

// Initialize answers object
const initializeAnswers = () => {
  const initialAnswers = {}

  props.questions.forEach(question => {
    if (question.type === 'checkbox') {
      initialAnswers[question._id] = []
    } else {
      initialAnswers[question._id] = ''
    }
  })
  answers.value = initialAnswers
}

// Initialize when questions change
watch(() => props.questions, () => {
  initializeAnswers()
}, { immediate: true })

// Validation
const isFormValid = computed(() => {
  return props.questions.every(question => {
    if (!question.required) return true
    
    const answer = answers.value[question._id]
    
    if (question.type === 'checkbox') {
      return Array.isArray(answer) && answer.length > 0
    }
    
    return answer && answer.toString().trim() !== ''
  })
})

// Methods
function handleSubmit() {
  if (!isFormValid.value) {
    return
  }
  
  const formData = {
    questionnaire: props.questionnaire._id,
    answers: answers.value,
    submittedAt: new Date(),
  }
  
  emit('submit', formData)
}

function handleCheckboxChange(questionId, option, checked) {
  if (!answers.value[questionId]) {
    answers.value[questionId] = []
  }
  
  if (checked) {
    if (!answers.value[questionId].includes(option)) {
      answers.value[questionId].push(option)
    }
  } else {
    const index = answers.value[questionId].indexOf(option)
    if (index > -1) {
      answers.value[questionId].splice(index, 1)
    }
  }
}

function getQuestionIcon(type) {
  const icons = {
    text: 'tabler-forms',
    textarea: 'tabler-file-text',
    select: 'tabler-chevron-down',
    radio: 'tabler-circle-dot',
    checkbox: 'tabler-square-check',
    'multiple-choice': 'tabler-list',
    number: 'tabler-123',
    email: 'tabler-mail',
    date: 'tabler-calendar',
    url: 'tabler-link',
    tel: 'tabler-phone',
  }

  
  return icons[type] || 'tabler-help'
}

function getInputType(questionType) {
  const typeMap = {
    text: 'text',
    number: 'number',
    email: 'email',
    date: 'date',
    url: 'url',
    tel: 'tel',
  }

  
  return typeMap[questionType] || 'text'
}

// Computed
const sortedQuestions = computed(() => {
  return [...props.questions].sort((a, b) => (a.order || 0) - (b.order || 0))
})
</script>

<template>
  <div class="questionnaire-renderer">
    <!-- Questionnaire Header -->
    <VCard class="mb-6">
      <VCardTitle class="d-flex align-center">
        <VIcon
          icon="tabler-clipboard-list"
          class="me-2"
          color="primary"
        />
        {{ questionnaire.title }}
      </VCardTitle>
      
      <VCardText v-if="questionnaire.description">
        <p class="text-body-1">
          {{ questionnaire.description }}
        </p>
      </VCardText>
    </VCard>

    <!-- Questions Form -->
    <VForm @submit.prevent="handleSubmit">
      <div
        v-for="(question, index) in sortedQuestions"
        :key="question._id"
        class="mb-6"
      >
        <VCard>
          <VCardText>
            <!-- Question Header -->
            <div class="d-flex align-center mb-4">
              <VChip
                size="small"
                color="primary"
                variant="tonal"
                class="me-3"
              >
                {{ index + 1 }}
              </VChip>
              
              <VIcon
                :icon="getQuestionIcon(question.type)"
                size="20"
                class="me-2 text-medium-emphasis"
              />
              
              <span class="text-h6">{{ question.text }}</span>
              
              <VChip
                v-if="question.required"
                size="x-small"
                color="error"
                variant="tonal"
                class="ms-2"
              >
                Required
              </VChip>
            </div>

            <!-- Question Input Based on Type -->
            <div class="question-input">
              <!-- Text Input -->
              <AppTextField
                v-if="question.type === 'text'"
                v-model="answers[question._id]"
                :placeholder="question.placeholder"
                :readonly="readonly"
                variant="outlined"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              />

              <!-- Textarea -->
              <AppTextarea
                v-else-if="question.type === 'textarea'"
                v-model="answers[question._id]"
                :placeholder="question.placeholder"
                :readonly="readonly"
                variant="outlined"
                rows="4"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              />

              <!-- Number Input -->
              <AppTextField
                v-else-if="question.type === 'number'"
                v-model="answers[question._id]"
                :placeholder="question.placeholder"
                :readonly="readonly"
                type="number"
                variant="outlined"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              />

              <!-- Email Input -->
              <AppTextField
                v-else-if="question.type === 'email'"
                v-model="answers[question._id]"
                :placeholder="question.placeholder"
                :readonly="readonly"
                type="email"
                variant="outlined"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              />

              <!-- Date Input -->
              <AppTextField
                v-else-if="question.type === 'date'"
                v-model="answers[question._id]"
                :placeholder="question.placeholder"
                :readonly="readonly"
                type="date"
                variant="outlined"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              />

              <!-- URL Input -->
              <AppTextField
                v-else-if="question.type === 'url'"
                v-model="answers[question._id]"
                :placeholder="question.placeholder"
                :readonly="readonly"
                type="url"
                variant="outlined"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              />

              <!-- Phone Input -->
              <AppTextField
                v-else-if="question.type === 'tel'"
                v-model="answers[question._id]"
                :placeholder="question.placeholder"
                :readonly="readonly"
                type="tel"
                variant="outlined"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              />

              <!-- Select Dropdown -->
              <VSelect
                v-else-if="question.type === 'select'"
                v-model="answers[question._id]"
                :items="question.options || []"
                :placeholder="question.placeholder || 'Select an option'"
                :readonly="readonly"
                variant="outlined"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              />

              <!-- Radio Buttons -->
              <VRadioGroup
                v-else-if="question.type === 'radio' || question.type === 'multiple-choice'"
                v-model="answers[question._id]"
                :readonly="readonly"
                :rules="question.required ? [v => !!v || 'This field is required'] : []"
              >
                <VRadio
                  v-for="option in question.options || []"
                  :key="option"
                  :label="option"
                  :value="option"
                />
              </VRadioGroup>

              <!-- Checkboxes -->
              <div v-else-if="question.type === 'checkbox'">
                <div
                  v-for="option in question.options || []"
                  :key="option"
                  class="mb-2"
                >
                  <VCheckbox
                    :model-value="answers[question._id]?.includes(option)"
                    :label="option"
                    :readonly="readonly"
                    @update:model-value="handleCheckboxChange(question._id, option, $event)"
                  />
                </div>
              </div>

              <!-- Fallback for unknown types -->
              <VAlert
                v-else
                type="warning"
                variant="tonal"
                class="mb-0"
              >
                Unsupported question type: {{ question.type }}
              </VAlert>
            </div>
          </VCardText>
        </VCard>
      </div>

      <!-- Submit Button -->
      <div
        v-if="showSubmit && !readonly"
        class="text-center mt-6"
      >
        <VBtn
          type="submit"
          color="primary"
          size="large"
          :disabled="!isFormValid"
          prepend-icon="tabler-send"
        >
          Submit Questionnaire
        </VBtn>
      </div>

      <!-- Empty State -->
      <div
        v-if="sortedQuestions.length === 0"
        class="text-center py-12"
      >
        <VIcon
          icon="tabler-help"
          size="64"
          class="text-medium-emphasis mb-4"
        />
        <h3 class="text-h6 mb-2">
          No Questions Available
        </h3>
        <p class="text-body-2 text-medium-emphasis">
          This questionnaire doesn't have any questions yet.
        </p>
      </div>
    </VForm>
  </div>
</template>

<style lang="scss" scoped>
.questionnaire-renderer {
  .question-input {
    max-width: 600px;
  }
}
</style>
