<script setup>
const props = defineProps({
  questionnaire: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits([
  'edit',
  'view',
  'delete',
  'builder',
  'respond',
])

// Methods
function handleEdit() {
  emit('edit', props.questionnaire)
}

function handleView() {
  emit('view', props.questionnaire)
}

function handleDelete() {
  emit('delete', props.questionnaire)
}

function handleBuilder() {
  emit('builder', props.questionnaire)
}

function handleRespond() {
  emit('respond', props.questionnaire)
}

function formatDate(date) {
  return new Date(date).toLocaleDateString()
}

function getStatusColor(isActive) {
  return isActive ? 'success' : 'warning'
}

function getStatusText(isActive) {
  return isActive ? 'Active' : 'Inactive'
}
</script>

<template>
  <VCard
    class="questionnaire-card"
    hover
    @click="handleView"
  >
    <!-- Card Header -->
    <VCardTitle class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <VIcon
          icon="tabler-clipboard-list"
          class="me-2"
          color="primary"
        />
        <span class="text-truncate">{{ questionnaire.title }}</span>
      </div>
      
      <VChip
        :color="getStatusColor(questionnaire.isActive)"
        size="small"
        variant="tonal"
      >
        {{ getStatusText(questionnaire.isActive) }}
      </VChip>
    </VCardTitle>

    <!-- Card Content -->
    <VCardText>
      <p class="text-body-2 text-medium-emphasis mb-3 text-truncate-3">
        {{ questionnaire.description || 'No description provided' }}
      </p>
      
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-calendar"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            Created {{ formatDate(questionnaire.createdAt) }}
          </span>
        </div>
        
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-user"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ questionnaire.user?.name || 'Unknown' }}
          </span>
        </div>
      </div>
    </VCardText>

    <!-- Card Actions -->
    <VCardActions>
      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-eye"
        @click.stop="handleView"
      >
        View
      </VBtn>
      
      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-edit"
        @click.stop="handleEdit"
      >
        Edit
      </VBtn>

      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-settings"
        @click.stop="handleBuilder"
      >
        Builder
      </VBtn>

      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-forms"
        color="success"
        @click.stop="handleRespond"
      >
        Respond
      </VBtn>

      <VSpacer />

      <VBtn
        variant="text"
        size="small"
        color="error"
        icon
        @click.stop="handleDelete"
      >
        <VIcon icon="tabler-trash" />
      </VBtn>
    </VCardActions>
  </VCard>
</template>

<style lang="scss" scoped>
.questionnaire-card {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
