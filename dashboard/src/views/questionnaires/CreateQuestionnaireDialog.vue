<script setup>
import { ref, computed, watch, toRefs } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketQuestionnaireStore } from '@stores/questionnaires'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible } = toRefs(props)

const storeAuth = useSocketStore()
const questionnaireStore = useSocketQuestionnaireStore()

const { user } = storeToRefs(storeAuth)

// Form data
const questionnaireForm = ref({
  title: '',
  description: '',
  isActive: true,
})

// Form validation
const isFormValid = computed(() => {
  return questionnaireForm.value.title.trim() !== ''
})

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  questionnaireStore.createQuestionnaire({
    user: user.value._id,
    questionnaire: questionnaireForm.value,
  })
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  questionnaireForm.value = {
    title: '',
    description: '',
    isActive: true,
  }
}

// Watch dialog close to reset form
watch(isDialogVisible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="600"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Create Questionnaire</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="questionnaireForm.title"
                label="Questionnaire Title"
                placeholder="Enter a descriptive title"
                :rules="[v => !!v || 'Title is required']"
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="questionnaireForm.description"
                label="Description (Optional)"
                placeholder="Describe the purpose of this questionnaire"
                rows="3"
              />
            </VCol>

            <VCol cols="12">
              <VCheckbox
                v-model="questionnaireForm.isActive"
                label="Active questionnaire"
                density="compact"
              />
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="outlined"
          @click="handleDialogClose"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          Create Questionnaire
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
