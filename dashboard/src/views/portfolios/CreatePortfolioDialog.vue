<script setup>
import { ref, computed, watch, toRefs } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketPortfolioStore } from '@stores/portfolios'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible } = toRefs(props)

const storeAuth = useSocketStore()
const portfolioStore = useSocketPortfolioStore()

const { user } = storeToRefs(storeAuth)

// Form data
const portfolioForm = ref({
  title: '',
  description: '',
  published: false,
})

// Form validation
const isFormValid = computed(() => {
  return portfolioForm.value.title.trim() !== ''
})

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  portfolioStore.createPortfolio({
    user: user.value._id,
    title: portfolioForm.value.title,
    description: portfolioForm.value.description,
    published: portfolioForm.value.published,
  })
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  portfolioForm.value = {
    title: '',
    description: '',
    published: false,
  }
}

// Watch dialog close to reset form
watch(isDialogVisible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="600"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Create Portfolio</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="portfolioForm.title"
                label="Portfolio Title"
                placeholder="Enter a descriptive title"
                :rules="[v => !!v || 'Title is required']"
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="portfolioForm.description"
                label="Description (Optional)"
                placeholder="Describe your portfolio"
                rows="3"
              />
            </VCol>

            <VCol cols="12">
              <VCheckbox
                v-model="portfolioForm.published"
                label="Publish portfolio immediately"
                density="compact"
              />
              <div class="text-caption text-medium-emphasis mt-1">
                You can change this later in the portfolio settings
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="outlined"
          @click="handleDialogClose"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          Create Portfolio
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
