<script setup>
import { ref } from 'vue'
import { toast } from 'vue3-toastify'

const props = defineProps({
  moodBoard: {
    type: Object,
    required: true,
  },
  saving: {
    type: Boolean,
    default: false,
  },
  sidebarOpen: {
    type: Boolean,
    default: true,
  },
  hasUnsavedChanges: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'save',
  'toggleSidebar',
])

// State
const showShareDialog = ref(false)
const showExportDialog = ref(false)

// Methods
function handleSave() {
  emit('save')
}

function handleToggleSidebar() {
  emit('toggleSidebar')
}

function handleShare() {
  showShareDialog.value = true
}

function handleExport() {
  showExportDialog.value = true
}

function handlePreview() {
  // Open preview in new tab
  const previewUrl = `/business-tools/mood-boards/${props.moodBoard._id}`

  window.open(previewUrl, '_blank')
}

function copyShareLink() {
  const shareUrl = `${window.location.origin}/business-tools/mood-boards/${props.moodBoard._id}`

  navigator.clipboard.writeText(shareUrl).then(() => {
    toast('Share link copied to clipboard!', {
      autoClose: 3000,
      type: 'success',
    })
  })
}

function exportAsImage() {
  // This would implement canvas-to-image export
  toast('Export functionality coming soon!', {
    autoClose: 3000,
    type: 'info',
  })
}

function exportAsPDF() {
  // This would implement PDF export
  toast('PDF export functionality coming soon!', {
    autoClose: 3000,
    type: 'info',
  })
}

function shareOnSocial(platform) {
  const url = encodeURIComponent(`${window.location.origin}/business-tools/mood-boards/${props.moodBoard._id}`)
  const text = encodeURIComponent(`Check out this mood board: ${props.moodBoard.title}`)

  let shareUrl = ''

  switch (platform) {
  case 'facebook':
    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`
    break
  case 'twitter':
    shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${text}`
    break
  case 'linkedin':
    shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`
    break
  default:
    return
  }

  window.open(shareUrl, '_blank', 'width=600,height=400')
}
</script>

<template>
  <VAppBar
    color="white"
    elevation="1"
    height="64"
  >
    <VContainer
      fluid
      class="d-flex align-center"
    >
      <!-- Left Section -->
      <div class="d-flex align-center gap-4">
        <VBtn
          icon
          variant="text"
          to="/business-tools/mood-boards"
        >
          <VIcon icon="tabler-arrow-left" />
        </VBtn>
        
        <div>
          <h6 class="text-h6 font-weight-medium">
            {{ moodBoard.title }}
          </h6>
          <div class="text-caption text-medium-emphasis">
            {{ saving ? 'Saving...' : hasUnsavedChanges ? 'Unsaved changes' : 'All changes saved' }}
          </div>
        </div>
      </div>

      <VSpacer />

      <!-- Center Section -->
      <div class="d-flex align-center gap-2">
        <VBtn
          variant="outlined"
          size="small"
          prepend-icon="tabler-eye"
          @click="handlePreview"
        >
          Preview
        </VBtn>
        
        <VBtn
          variant="outlined"
          size="small"
          prepend-icon="tabler-share"
          @click="handleShare"
        >
          Share
        </VBtn>
        
        <VBtn
          variant="outlined"
          size="small"
          prepend-icon="tabler-download"
          @click="handleExport"
        >
          Export
        </VBtn>
      </div>

      <VSpacer />

      <!-- Right Section -->
      <div class="d-flex align-center gap-2">
        <VBtn
          :variant="sidebarOpen ? 'flat' : 'outlined'"
          :color="sidebarOpen ? 'primary' : 'default'"
          size="small"
          icon
          @click="handleToggleSidebar"
        >
          <VIcon icon="tabler-layout-sidebar-right" />
        </VBtn>
        
        <VBtn
          color="primary"
          :loading="saving"
          @click="handleSave"
        >
          Save
          <VTooltip activator="parent">
            Save (Ctrl+S)
          </VTooltip>
        </VBtn>
      </div>
    </VContainer>

    <!-- Share Dialog -->
    <VDialog
      v-model="showShareDialog"
      max-width="500"
    >
      <VCard>
        <VCardTitle>Share Mood Board</VCardTitle>
        <VCardText>
          <div class="mb-4">
            <VLabel class="mb-2">
              Share Link
            </VLabel>
            <VTextField
              :model-value="`${window.location.origin}/business-tools/mood-boards/${moodBoard._id}`"
              readonly
              variant="outlined"
              density="compact"
            >
              <template #append-inner>
                <VBtn
                  variant="text"
                  size="small"
                  @click="copyShareLink"
                >
                  Copy
                </VBtn>
              </template>
            </VTextField>
          </div>
          
          <div class="d-flex gap-2">
            <VBtn
              color="primary"
              variant="outlined"
              prepend-icon="tabler-brand-facebook"
              @click="shareOnSocial('facebook')"
            >
              Facebook
            </VBtn>
            
            <VBtn
              color="primary"
              variant="outlined"
              prepend-icon="tabler-brand-twitter"
              @click="shareOnSocial('twitter')"
            >
              Twitter
            </VBtn>
            
            <VBtn
              color="primary"
              variant="outlined"
              prepend-icon="tabler-brand-linkedin"
              @click="shareOnSocial('linkedin')"
            >
              LinkedIn
            </VBtn>
          </div>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn @click="showShareDialog = false">
            Close
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Export Dialog -->
    <VDialog
      v-model="showExportDialog"
      max-width="400"
    >
      <VCard>
        <VCardTitle>Export Mood Board</VCardTitle>
        <VCardText>
          <VList>
            <VListItem @click="exportAsImage">
              <template #prepend>
                <VIcon icon="tabler-photo" />
              </template>
              <VListItemTitle>Export as Image</VListItemTitle>
              <VListItemSubtitle>PNG format, high quality</VListItemSubtitle>
            </VListItem>
            
            <VListItem @click="exportAsPDF">
              <template #prepend>
                <VIcon icon="tabler-file-type-pdf" />
              </template>
              <VListItemTitle>Export as PDF</VListItemTitle>
              <VListItemSubtitle>Vector format, print ready</VListItemSubtitle>
            </VListItem>
          </VList>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn @click="showExportDialog = false">
            Close
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </VAppBar>
</template>

<style lang="scss" scoped>
.v-app-bar {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}
</style>
