<script setup>
import { ref, onUnmounted } from 'vue'
import { toast } from 'vue3-toastify'
import {
  validateFile,
  fileToBase64,
  getImageDimensions,
  formatFileSize,
  generateThumbnail,
} from '@/utils/fileUpload'

const props = defineProps({
  moodBoard: {
    type: Object,
    required: true,
  },
  selectedItem: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits([
  'addItem',
  'updateItem',
  'deleteItem',
])

// State
const activeTab = ref('add')
const imageUrl = ref('')
const textContent = ref('')
const colorValue = ref('#ff5722')
const linkUrl = ref('')
const linkTitle = ref('')
const selectedFiles = ref(null)
const uploading = ref(false)
const isDragOver = ref(false)
const fileInputRef = ref(null)
const uploadProgress = ref({ current: 0, total: 0 })

// Predefined colors
const predefinedColors = [
  '#f44336',
  '#e91e63',
  '#9c27b0',
  '#673ab7',
  '#3f51b5',
  '#2196f3',
  '#03a9f4',
  '#00bcd4',
  '#009688',
  '#4caf50',
  '#8bc34a',
  '#cddc39',
  '#ffeb3b',
  '#ffc107',
  '#ff9800',
  '#ff5722',
  '#795548',
  '#9e9e9e',
  '#607d8b',
  '#000000',
]

// Methods
function addImage() {
  if (!imageUrl.value.trim()) return
  
  const item = {
    type: 'image',
    content: imageUrl.value.trim(),
    position: { x: 100, y: 100 },
    size: { width: 200, height: 200 },
    style: {
      borderRadius: 0,
      opacity: 1,
      rotation: 0,
      zIndex: 1,
    },
    metadata: {
      title: '',
      description: '',
      source: imageUrl.value.trim(),
    },
  }
  
  emit('addItem', item)
  imageUrl.value = ''
}

function addColor() {
  const item = {
    type: 'color',
    content: colorValue.value,
    position: { x: 100, y: 100 },
    size: { width: 100, height: 100 },
    style: {
      borderRadius: 8,
      opacity: 1,
      rotation: 0,
      zIndex: 1,
    },
    metadata: {
      title: colorValue.value,
    },
  }
  
  emit('addItem', item)
}

function addText() {
  if (!textContent.value.trim()) return
  
  const item = {
    type: 'text',
    content: textContent.value.trim(),
    position: { x: 100, y: 100 },
    size: { width: 200, height: 100 },
    style: {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderRadius: 4,
      opacity: 1,
      rotation: 0,
      zIndex: 1,
    },
    metadata: {
      fontSize: 16,
      fontFamily: 'Arial, sans-serif',
      fontWeight: 'normal',
      textAlign: 'left',
      color: '#000000',
    },
  }
  
  emit('addItem', item)
  textContent.value = ''
}

function addLink() {
  if (!linkUrl.value.trim()) return
  
  const item = {
    type: 'link',
    content: linkUrl.value.trim(),
    position: { x: 100, y: 100 },
    size: { width: 200, height: 50 },
    style: {
      borderRadius: 8,
      opacity: 1,
      rotation: 0,
      zIndex: 1,
    },
    metadata: {
      title: linkTitle.value.trim() || linkUrl.value.trim(),
    },
  }
  
  emit('addItem', item)
  linkUrl.value = ''
  linkTitle.value = ''
}

// Debounced update to prevent rapid API calls
let updateTimeout = null
function updateSelectedItem(updates) {
  if (!props.selectedItem) return

  // Clear existing timeout
  if (updateTimeout) {
    clearTimeout(updateTimeout)
  }

  // Update local state immediately for responsive UI
  const updatedItem = {
    ...props.selectedItem,
    ...updates,
  }

  // Ensure we have the correct ID
  if (!updatedItem._id) {
    toast('Error: Item ID not found', {
      autoClose: 3000,
      type: 'error',
    })
    
    return
  }

  // Debounce the actual emit to prevent rapid updates
  updateTimeout = setTimeout(() => {
    emit('updateItem', updatedItem)
  }, 300) // 300ms debounce
}

function deleteSelectedItem() {
  if (!props.selectedItem) return
  
  if (confirm('Are you sure you want to delete this item?')) {
    emit('deleteItem', props.selectedItem._id)
  }
}

function selectPredefinedColor(color) {
  colorValue.value = color
  addColor()
}

// File upload methods
function handleFileSelect(files) {
  selectedFiles.value = files
}

// Drag & Drop methods
function handleDragOver(event) {
  event.preventDefault()
  isDragOver.value = true
}

function handleDragLeave(event) {
  event.preventDefault()
  isDragOver.value = false
}

function handleDrop(event) {
  event.preventDefault()
  isDragOver.value = false

  const files = Array.from(event.dataTransfer.files)
  if (files.length > 0) {
    selectedFiles.value = files
  }
}

function triggerFileInput() {
  if (!uploading.value && fileInputRef.value) {
    fileInputRef.value.click()
  }
}

function handleFileInputChange(event) {
  const files = Array.from(event.target.files)
  if (files.length > 0) {
    selectedFiles.value = files
  }
}

function clearSelectedFile() {
  selectedFiles.value = null
  uploadProgress.value = { current: 0, total: 0 }
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

function removeFile(index) {
  if (selectedFiles.value) {
    selectedFiles.value.splice(index, 1)
    if (selectedFiles.value.length === 0) {
      clearSelectedFile()
    }
  }
}

async function uploadAndAddImages() {
  // Handle both single and multiple files
  if (!selectedFiles.value || selectedFiles.value.length === 0) return

  if (selectedFiles.value.length === 1) {
    return uploadSingleImage()
  } else {
    return uploadMultipleImages()
  }
}

async function uploadSingleImage() {
  if (!selectedFiles.value || selectedFiles.value.length === 0) return

  const file = selectedFiles.value[0]

  // Validate file
  const validation = validateFile(file, 'IMAGE')
  if (!validation.isValid) {
    toast(validation.error, {
      autoClose: 3000,
      type: 'error',
    })
    
    return
  }

  uploading.value = true

  try {
    // Get image dimensions for better sizing
    const dimensions = await getImageDimensions(file)

    // Convert file to base64
    const base64 = await fileToBase64(file)

    // Calculate appropriate size for the mood board
    let itemWidth = 200
    let itemHeight = 200

    if (dimensions.width && dimensions.height) {
      const aspectRatio = dimensions.width / dimensions.height
      if (aspectRatio > 1) {
        // Landscape
        itemWidth = 250
        itemHeight = Math.round(250 / aspectRatio)
      } else {
        // Portrait or square
        itemHeight = 250
        itemWidth = Math.round(250 * aspectRatio)
      }
    }

    const item = {
      type: 'image',
      content: base64,
      position: { x: 100, y: 100 },
      size: { width: itemWidth, height: itemHeight },
      style: {
        borderRadius: 0,
        opacity: 1,
        rotation: 0,
        zIndex: 1,
      },
      metadata: {
        title: file.name,
        description: `Uploaded: ${file.name} (${formatFileSize(file.size)})`,
        source: 'upload',
        fileSize: file.size,
        fileType: file.type,
        originalDimensions: dimensions,
      },
    }

    emit('addItem', item)
    selectedFiles.value = null

    toast(`Image "${file.name}" uploaded successfully!`, {
      autoClose: 3000,
      type: 'success',
    })
  } catch (error) {
    console.error('Upload error:', error)
    toast('Failed to upload image: ' + error.message, {
      autoClose: 5000,
      type: 'error',
    })
  } finally {
    uploading.value = false
  }
}

// Bulk upload method for multiple images
async function uploadMultipleImages() {
  if (!selectedFiles.value || selectedFiles.value.length === 0) return

  uploading.value = true
  uploadProgress.value = { current: 0, total: selectedFiles.value.length }
  let successCount = 0
  let errorCount = 0

  try {
    for (let i = 0; i < selectedFiles.value.length; i++) {
      uploadProgress.value.current = i + 1

      const file = selectedFiles.value[i]

      try {
        // Validate each file
        const validation = validateFile(file, 'IMAGE')
        if (!validation.isValid) {
          console.warn(`Skipping ${file.name}: ${validation.error}`)
          errorCount++
          continue
        }

        // Get image dimensions
        const dimensions = await getImageDimensions(file)

        // Convert to base64
        const base64 = await fileToBase64(file)

        // Calculate size
        let itemWidth = 200
        let itemHeight = 200

        if (dimensions.width && dimensions.height) {
          const aspectRatio = dimensions.width / dimensions.height
          if (aspectRatio > 1) {
            itemWidth = 250
            itemHeight = Math.round(250 / aspectRatio)
          } else {
            itemHeight = 250
            itemWidth = Math.round(250 * aspectRatio)
          }
        }

        // Position images in a grid pattern
        const gridCols = 3
        const spacing = 20
        const startX = 100
        const startY = 100

        const col = i % gridCols
        const row = Math.floor(i / gridCols)

        const x = startX + col * (itemWidth + spacing)
        const y = startY + row * (itemHeight + spacing)

        const item = {
          type: 'image',
          content: base64,
          position: { x, y },
          size: { width: itemWidth, height: itemHeight },
          style: {
            borderRadius: 0,
            opacity: 1,
            rotation: 0,
            zIndex: 1,
          },
          metadata: {
            title: file.name,
            description: `Uploaded: ${file.name} (${formatFileSize(file.size)})`,
            source: 'upload',
            fileSize: file.size,
            fileType: file.type,
            originalDimensions: dimensions,
          },
        }

        emit('addItem', item)
        successCount++

        // Small delay to prevent overwhelming the UI
        if (i < selectedFiles.value.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      } catch (error) {
        console.error(`Error uploading ${file.name}:`, error)
        errorCount++
      }
    }

    // Show result message
    if (successCount > 0 && errorCount === 0) {
      toast(`Successfully uploaded ${successCount} image${successCount > 1 ? 's' : ''}!`, {
        autoClose: 3000,
        type: 'success',
      })
    } else if (successCount > 0 && errorCount > 0) {
      toast(`Uploaded ${successCount} images, ${errorCount} failed`, {
        autoClose: 5000,
        type: 'warning',
      })
    } else {
      toast('Failed to upload images', {
        autoClose: 5000,
        type: 'error',
      })
    }

    selectedFiles.value = null
    uploadProgress.value = { current: 0, total: 0 }
    if (fileInputRef.value) {
      fileInputRef.value.value = ''
    }
  } catch (error) {
    console.error('Bulk upload error:', error)
    toast('Failed to upload images: ' + error.message, {
      autoClose: 5000,
      type: 'error',
    })
  } finally {
    uploading.value = false
    uploadProgress.value = { current: 0, total: 0 }
  }
}



// Cleanup timeout on unmount
onUnmounted(() => {
  if (updateTimeout) {
    clearTimeout(updateTimeout)
  }
})
</script>

<template>
  <div class="mood-board-sidebar">
    <VTabs
      v-model="activeTab"
      color="primary"
    >
      <VTab value="add">
        Add Items
      </VTab>
      <VTab
        value="edit"
        :disabled="!selectedItem"
      >
        Edit Item
      </VTab>
    </VTabs>

    <VTabsWindow
      v-model="activeTab"
      class="sidebar-content"
    >
      <!-- Add Items Tab -->
      <VTabsWindowItem value="add">
        <div class="pa-4">
          <!-- Add Image -->
          <VExpansionPanels class="mb-4">
            <VExpansionPanel>
              <VExpansionPanelTitle>
                <VIcon
                  icon="tabler-photo"
                  class="me-2"
                />
                Add Image
              </VExpansionPanelTitle>
              <VExpansionPanelText>
                <!-- File Upload -->
                <div class="mb-3">
                  <VLabel class="mb-2">
                    Upload Image
                  </VLabel>

                  <!-- Drag & Drop Area -->
                  <div
                    class="file-drop-zone"
                    :class="{ 'drag-over': isDragOver, 'uploading': uploading }"
                    @dragover.prevent="handleDragOver"
                    @dragleave.prevent="handleDragLeave"
                    @drop.prevent="handleDrop"
                    @click="triggerFileInput"
                  >
                    <VIcon
                      :icon="uploading ? 'tabler-loader-2' : 'tabler-cloud-upload'"
                      size="32"
                      :class="{ 'rotating': uploading }"
                      class="mb-2"
                    />
                    <div class="text-body-2 mb-1">
                      {{ uploading ? 'Uploading...' : 'Drop image here or click to browse' }}
                    </div>
                    <div class="text-caption text-medium-emphasis">
                      Supports: JPG, PNG, GIF, WebP (max 5MB)
                    </div>
                  </div>

                  <!-- Hidden File Input -->
                  <input
                    ref="fileInputRef"
                    type="file"
                    accept="image/*"
                    multiple
                    style="display: none"
                    @change="handleFileInputChange"
                  >

                  <!-- Selected Files Info -->
                  <div
                    v-if="selectedFiles && selectedFiles.length > 0"
                    class="mt-3"
                  >
                    <div class="text-caption text-medium-emphasis mb-2">
                      {{ selectedFiles.length }} file{{ selectedFiles.length > 1 ? 's' : '' }} selected
                    </div>

                    <div class="selected-files-list">
                      <VCard
                        v-for="(file, index) in selectedFiles.slice(0, 3)"
                        :key="index"
                        variant="outlined"
                        class="pa-2 mb-2"
                      >
                        <div class="d-flex align-center">
                          <VIcon
                            icon="tabler-photo"
                            size="16"
                            class="me-2"
                          />
                          <div class="flex-grow-1">
                            <div class="text-body-2 font-weight-medium text-truncate">
                              {{ file.name }}
                            </div>
                            <div class="text-caption text-medium-emphasis">
                              {{ formatFileSize(file.size) }}
                            </div>
                          </div>
                          <VBtn
                            icon
                            size="x-small"
                            variant="text"
                            @click="removeFile(index)"
                          >
                            <VIcon
                              icon="tabler-x"
                              size="12"
                            />
                          </VBtn>
                        </div>
                      </VCard>

                      <div
                        v-if="selectedFiles.length > 3"
                        class="text-caption text-medium-emphasis text-center"
                      >
                        +{{ selectedFiles.length - 3 }} more files
                      </div>
                    </div>

                    <!-- Upload Progress -->
                    <div
                      v-if="uploading && uploadProgress.total > 1"
                      class="mt-2"
                    >
                      <div class="d-flex justify-space-between text-caption mb-1">
                        <span>Uploading...</span>
                        <span>{{ uploadProgress.current }} / {{ uploadProgress.total }}</span>
                      </div>
                      <VProgressLinear
                        :model-value="(uploadProgress.current / uploadProgress.total) * 100"
                        color="primary"
                        height="4"
                      />
                    </div>

                    <VBtn
                      color="primary"
                      block
                      :loading="uploading"
                      :disabled="uploading"
                      class="mt-3"
                      @click="uploadAndAddImages"
                    >
                      <template v-if="uploading && uploadProgress.total > 1">
                        Uploading {{ uploadProgress.current }} / {{ uploadProgress.total }}
                      </template>
                      <template v-else>
                        Upload {{ selectedFiles.length }} Image{{ selectedFiles.length > 1 ? 's' : '' }}
                      </template>
                    </VBtn>
                  </div>
                </div>

                <VDivider class="my-4" />

                <!-- URL Input -->
                <div>
                  <VLabel class="mb-2">
                    Or use Image URL
                  </VLabel>
                  <VTextField
                    v-model="imageUrl"
                    label="Image URL"
                    placeholder="https://example.com/image.jpg"
                    variant="outlined"
                    density="compact"
                    class="mb-3"
                  />
                  <VBtn
                    color="primary"
                    variant="outlined"
                    block
                    :disabled="!imageUrl.trim()"
                    @click="addImage"
                  >
                    Add from URL
                  </VBtn>
                </div>
              </VExpansionPanelText>
            </VExpansionPanel>
          </VExpansionPanels>

          <!-- Add Color -->
          <VExpansionPanels class="mb-4">
            <VExpansionPanel>
              <VExpansionPanelTitle>
                <VIcon
                  icon="tabler-palette"
                  class="me-2"
                />
                Add Color
              </VExpansionPanelTitle>
              <VExpansionPanelText>
                <VColorPicker
                  v-model="colorValue"
                  mode="hex"
                  hide-inputs
                  class="mb-3"
                />
                
                <div class="mb-3">
                  <VLabel class="mb-2">
                    Quick Colors
                  </VLabel>
                  <div class="color-grid">
                    <div
                      v-for="color in predefinedColors"
                      :key="color"
                      class="color-swatch"
                      :style="{ backgroundColor: color }"
                      @click="selectPredefinedColor(color)"
                    />
                  </div>
                </div>
                
                <VBtn
                  color="primary"
                  block
                  @click="addColor"
                >
                  Add Color
                </VBtn>
              </VExpansionPanelText>
            </VExpansionPanel>
          </VExpansionPanels>

          <!-- Add Text -->
          <VExpansionPanels class="mb-4">
            <VExpansionPanel>
              <VExpansionPanelTitle>
                <VIcon
                  icon="tabler-typography"
                  class="me-2"
                />
                Add Text
              </VExpansionPanelTitle>
              <VExpansionPanelText>
                <VTextarea
                  v-model="textContent"
                  label="Text Content"
                  placeholder="Enter your text here..."
                  variant="outlined"
                  density="compact"
                  rows="3"
                  class="mb-3"
                />
                <VBtn
                  color="primary"
                  block
                  :disabled="!textContent.trim()"
                  @click="addText"
                >
                  Add Text
                </VBtn>
              </VExpansionPanelText>
            </VExpansionPanel>
          </VExpansionPanels>

          <!-- Add Link -->
          <VExpansionPanels>
            <VExpansionPanel>
              <VExpansionPanelTitle>
                <VIcon
                  icon="tabler-link"
                  class="me-2"
                />
                Add Link
              </VExpansionPanelTitle>
              <VExpansionPanelText>
                <VTextField
                  v-model="linkUrl"
                  label="URL"
                  placeholder="https://example.com"
                  variant="outlined"
                  density="compact"
                  class="mb-3"
                />
                <VTextField
                  v-model="linkTitle"
                  label="Title (Optional)"
                  placeholder="Link title"
                  variant="outlined"
                  density="compact"
                  class="mb-3"
                />
                <VBtn
                  color="primary"
                  block
                  :disabled="!linkUrl.trim()"
                  @click="addLink"
                >
                  Add Link
                </VBtn>
              </VExpansionPanelText>
            </VExpansionPanel>
          </VExpansionPanels>
        </div>
      </VTabsWindowItem>

      <!-- Edit Item Tab -->
      <VTabsWindowItem value="edit">
        <div
          v-if="selectedItem"
          class="pa-4"
        >
          <div class="d-flex align-center justify-space-between mb-4">
            <h6 class="text-h6">
              Edit {{ selectedItem.type }}
            </h6>
            <VBtn
              color="error"
              variant="outlined"
              size="small"
              @click="deleteSelectedItem"
            >
              Delete
            </VBtn>
          </div>

          <!-- Position -->
          <div class="mb-4">
            <VLabel class="mb-2">
              Position
            </VLabel>
            <VRow>
              <VCol cols="6">
                <VTextField
                  :model-value="selectedItem.position.x"
                  label="X"
                  type="number"
                  variant="outlined"
                  density="compact"
                  @update:model-value="updateSelectedItem({ position: { ...selectedItem.position, x: Number($event) } })"
                />
              </VCol>
              <VCol cols="6">
                <VTextField
                  :model-value="selectedItem.position.y"
                  label="Y"
                  type="number"
                  variant="outlined"
                  density="compact"
                  @update:model-value="updateSelectedItem({ position: { ...selectedItem.position, y: Number($event) } })"
                />
              </VCol>
            </VRow>
          </div>

          <!-- Size -->
          <div class="mb-4">
            <VLabel class="mb-2">
              Size
            </VLabel>
            <VRow>
              <VCol cols="6">
                <VTextField
                  :model-value="selectedItem.size.width"
                  label="Width"
                  type="number"
                  variant="outlined"
                  density="compact"
                  @update:model-value="updateSelectedItem({ size: { ...selectedItem.size, width: Number($event) } })"
                />
              </VCol>
              <VCol cols="6">
                <VTextField
                  :model-value="selectedItem.size.height"
                  label="Height"
                  type="number"
                  variant="outlined"
                  density="compact"
                  @update:model-value="updateSelectedItem({ size: { ...selectedItem.size, height: Number($event) } })"
                />
              </VCol>
            </VRow>
          </div>

          <!-- Style -->
          <div class="mb-4">
            <VLabel class="mb-2">
              Style
            </VLabel>
            
            <VTextField
              :model-value="selectedItem.style.rotation || 0"
              label="Rotation (degrees)"
              type="number"
              variant="outlined"
              density="compact"
              class="mb-3"
              @update:model-value="updateSelectedItem({ style: { ...selectedItem.style, rotation: Number($event) } })"
            />
            
            <VSlider
              :model-value="selectedItem.style.opacity || 1"
              label="Opacity"
              min="0"
              max="1"
              step="0.1"
              thumb-label
              class="mb-3"
              @update:model-value="updateSelectedItem({ style: { ...selectedItem.style, opacity: $event } })"
            />
            
            <VTextField
              :model-value="selectedItem.style.borderRadius || 0"
              label="Border Radius"
              type="number"
              variant="outlined"
              density="compact"
              @update:model-value="updateSelectedItem({ style: { ...selectedItem.style, borderRadius: Number($event) } })"
            />
          </div>

          <!-- Text-specific options -->
          <div
            v-if="selectedItem.type === 'text'"
            class="mb-4"
          >
            <VLabel class="mb-2">
              Text Style
            </VLabel>
            
            <VTextField
              :model-value="selectedItem.metadata.fontSize || 16"
              label="Font Size"
              type="number"
              variant="outlined"
              density="compact"
              class="mb-3"
              @update:model-value="updateSelectedItem({ metadata: { ...selectedItem.metadata, fontSize: Number($event) } })"
            />
            
            <VSelect
              :model-value="selectedItem.metadata.textAlign || 'left'"
              label="Text Align"
              :items="[
                { title: 'Left', value: 'left' },
                { title: 'Center', value: 'center' },
                { title: 'Right', value: 'right' }
              ]"
              variant="outlined"
              density="compact"
              class="mb-3"
              @update:model-value="updateSelectedItem({ metadata: { ...selectedItem.metadata, textAlign: $event } })"
            />
            
            <VColorPicker
              :model-value="selectedItem.metadata.color || '#000000'"
              mode="hex"
              hide-inputs
              @update:model-value="updateSelectedItem({ metadata: { ...selectedItem.metadata, color: $event } })"
            />
          </div>
        </div>
        
        <div
          v-else
          class="pa-4 text-center"
        >
          <VIcon
            icon="tabler-click"
            size="48"
            class="text-medium-emphasis mb-2"
          />
          <p class="text-body-2 text-medium-emphasis">
            Select an item on the canvas to edit its properties
          </p>
        </div>
      </VTabsWindowItem>
    </VTabsWindow>
  </div>
</template>

<style lang="scss" scoped>
.mood-board-sidebar {
  width: 320px;
  height: 100%;
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  background: white;
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.color-swatch {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: rgba(0, 0, 0, 0.2);
  }
}

.file-drop-zone {
  border: 2px dashed rgba(var(--v-border-color), var(--v-border-opacity));
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(var(--v-theme-surface), 0.5);

  &:hover {
    border-color: rgb(var(--v-theme-primary));
    background: rgba(var(--v-theme-primary), 0.05);
  }

  &.drag-over {
    border-color: rgb(var(--v-theme-primary));
    background: rgba(var(--v-theme-primary), 0.1);
    transform: scale(1.02);
  }

  &.uploading {
    pointer-events: none;
    opacity: 0.7;
  }
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
