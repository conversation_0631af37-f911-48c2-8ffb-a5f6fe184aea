<script setup>
const props = defineProps({
  moodBoard: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits([
  'edit',
  'view',
  'duplicate',
  'delete',
])

// Methods
function handleEdit() {
  emit('edit', props.moodBoard)
}

function handleView() {
  emit('view', props.moodBoard)
}

function handleDuplicate() {
  emit('duplicate', props.moodBoard)
}

function handleDelete() {
  emit('delete', props.moodBoard)
}

function formatDate(date) {
  return new Date(date).toLocaleDateString()
}

function getCategoryColor(category) {
  const colors = {
    branding: 'primary',
    'web-design': 'info',
    interior: 'success',
    fashion: 'warning',
    product: 'error',
    marketing: 'secondary',
    other: 'default',
  }

  
  return colors[category] || 'default'
}

function getPreviewImage() {
  // Find the first image item in the mood board
  const imageItem = props.moodBoard.items?.find(item => item.type === 'image')
  
  return imageItem?.content || null
}

function getItemsCount() {
  return props.moodBoard.items?.length || 0
}
</script>

<template>
  <VCard
    class="mood-board-card"
    hover
    @click="handleView"
  >
    <!-- Preview Image -->
    <div class="mood-board-preview">
      <VImg
        v-if="getPreviewImage()"
        :src="getPreviewImage()"
        height="200"
        cover
        class="mood-board-image"
      />
      <div
        v-else
        class="mood-board-placeholder d-flex align-center justify-center"
        :style="{ backgroundColor: moodBoard.settings?.backgroundColor || '#f5f5f5' }"
      >
        <VIcon
          icon="tabler-palette"
          size="48"
          class="text-medium-emphasis"
        />
      </div>
      
      <!-- Overlay Actions -->
      <div class="mood-board-overlay">
        <div class="d-flex gap-2">
          <VBtn
            icon
            size="small"
            color="white"
            variant="elevated"
            @click.stop="handleEdit"
          >
            <VIcon icon="tabler-edit" />
          </VBtn>
          
          <VBtn
            icon
            size="small"
            color="white"
            variant="elevated"
            @click.stop="handleDuplicate"
          >
            <VIcon icon="tabler-copy" />
          </VBtn>
          
          <VBtn
            icon
            size="small"
            color="error"
            variant="elevated"
            @click.stop="handleDelete"
          >
            <VIcon icon="tabler-trash" />
          </VBtn>
        </div>
      </div>
    </div>

    <!-- Card Content -->
    <VCardText>
      <div class="d-flex align-center justify-space-between mb-2">
        <h6 class="text-h6 text-truncate">
          {{ moodBoard.title }}
        </h6>
        <VChip
          v-if="moodBoard.templateCategory"
          :color="getCategoryColor(moodBoard.templateCategory)"
          size="small"
          variant="tonal"
        >
          {{ moodBoard.templateCategory }}
        </VChip>
      </div>
      
      <p class="text-body-2 text-medium-emphasis mb-3 text-truncate-2">
        {{ moodBoard.description || 'No description provided' }}
      </p>
      
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-photo"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ getItemsCount() }} items
          </span>
        </div>
        
        <div class="d-flex align-center gap-2">
          <VIcon
            v-if="moodBoard.isPublic"
            icon="tabler-world"
            size="16"
            class="text-medium-emphasis"
          />
          <VIcon
            v-if="moodBoard.isTemplate"
            icon="tabler-template"
            size="16"
            class="text-medium-emphasis"
          />
        </div>
      </div>
      
      <div class="text-caption text-medium-emphasis mt-2">
        Updated {{ formatDate(moodBoard.lastModified) }}
      </div>
    </VCardText>
  </VCard>
</template>

<style lang="scss" scoped>
.mood-board-card {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    
    .mood-board-overlay {
      opacity: 1;
    }
  }
}

.mood-board-preview {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.mood-board-placeholder {
  height: 200px;
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.mood-board-image {
  transition: transform 0.2s ease;
  
  .mood-board-card:hover & {
    transform: scale(1.05);
  }
}

.mood-board-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
