<script setup>
import { ref, computed, watch, toRefs } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketMoodBoardStore } from '@stores/mood-boards'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible } = toRefs(props)

const storeAuth = useSocketStore()
const moodBoardStore = useSocketMoodBoardStore()

const { user } = storeToRefs(storeAuth)

// Form data
const moodBoardForm = ref({
  title: '',
  description: '',
  templateCategory: '',
  isPublic: false,
  isTemplate: false,
  tags: [],
  settings: {
    backgroundColor: '#ffffff',
    canvasSize: {
      width: 1920,
      height: 1080,
    },
    gridSize: 20,
    showGrid: false,
    snapToGrid: false,
  },
})

// Categories
const categories = [
  { title: 'Branding', value: 'branding' },
  { title: 'Web Design', value: 'web-design' },
  { title: 'Interior', value: 'interior' },
  { title: 'Fashion', value: 'fashion' },
  { title: 'Product', value: 'product' },
  { title: 'Marketing', value: 'marketing' },
  { title: 'Other', value: 'other' },
]

// Canvas size presets
const canvasSizes = [
  { title: 'HD (1920x1080)', value: { width: 1920, height: 1080 } },
  { title: 'Square (1080x1080)', value: { width: 1080, height: 1080 } },
  { title: '4K (3840x2160)', value: { width: 3840, height: 2160 } },
  { title: 'A4 Portrait (2480x3508)', value: { width: 2480, height: 3508 } },
  { title: 'A4 Landscape (3508x2480)', value: { width: 3508, height: 2480 } },
  { title: 'Custom', value: 'custom' },
]

const selectedCanvasSize = ref(canvasSizes[0])

// Tag input
const tagInput = ref('')

// Form validation
const isFormValid = computed(() => {
  return moodBoardForm.value.title.trim() !== ''
})

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  // Set canvas size
  if (selectedCanvasSize.value.value !== 'custom') {
    moodBoardForm.value.settings.canvasSize = selectedCanvasSize.value.value
  }

  moodBoardStore.createMoodBoard({
    user: user.value._id,
    moodBoard: moodBoardForm.value,
  })
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Add tag
function addTag() {
  if (tagInput.value.trim() && !moodBoardForm.value.tags.includes(tagInput.value.trim())) {
    moodBoardForm.value.tags.push(tagInput.value.trim())
    tagInput.value = ''
  }
}

// Remove tag
function removeTag(index) {
  moodBoardForm.value.tags.splice(index, 1)
}

// Reset form
function resetForm() {
  moodBoardForm.value = {
    title: '',
    description: '',
    templateCategory: '',
    isPublic: false,
    isTemplate: false,
    tags: [],
    settings: {
      backgroundColor: '#ffffff',
      canvasSize: {
        width: 1920,
        height: 1080,
      },
      gridSize: 20,
      showGrid: false,
      snapToGrid: false,
    },
  }
  selectedCanvasSize.value = canvasSizes[0]
  tagInput.value = ''
}

// Watch dialog close to reset form
watch(isDialogVisible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="700"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Create Mood Board</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="moodBoardForm.title"
                label="Mood Board Title"
                placeholder="Enter a descriptive title"
                :rules="[v => !!v || 'Title is required']"
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="moodBoardForm.description"
                label="Description (Optional)"
                placeholder="Describe the purpose or theme of this mood board"
                rows="3"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppSelect
                v-model="moodBoardForm.templateCategory"
                label="Category (Optional)"
                :items="categories"
                item-title="title"
                item-value="value"
                placeholder="Select a category"
                clearable
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppSelect
                v-model="selectedCanvasSize"
                label="Canvas Size"
                :items="canvasSizes"
                item-title="title"
                return-object
              />
            </VCol>

            <!-- Custom canvas size -->
            <VCol
              v-if="selectedCanvasSize.value === 'custom'"
              cols="12"
            >
              <VRow>
                <VCol cols="6">
                  <AppTextField
                    v-model="moodBoardForm.settings.canvasSize.width"
                    label="Width (px)"
                    type="number"
                    min="100"
                  />
                </VCol>
                <VCol cols="6">
                  <AppTextField
                    v-model="moodBoardForm.settings.canvasSize.height"
                    label="Height (px)"
                    type="number"
                    min="100"
                  />
                </VCol>
              </VRow>
            </VCol>

            <VCol cols="12">
              <VLabel class="mb-2">
                Background Color
              </VLabel>
              <VColorPicker
                v-model="moodBoardForm.settings.backgroundColor"
                mode="hex"
                hide-inputs
                show-swatches
              />
            </VCol>

            <!-- Tags -->
            <VCol cols="12">
              <VLabel class="mb-2">
                Tags (Optional)
              </VLabel>
              <div class="d-flex gap-2 mb-2">
                <AppTextField
                  v-model="tagInput"
                  placeholder="Add a tag"
                  density="compact"
                  @keyup.enter="addTag"
                />
                <VBtn
                  color="primary"
                  variant="outlined"
                  @click="addTag"
                >
                  Add
                </VBtn>
              </div>
              
              <div
                v-if="moodBoardForm.tags.length > 0"
                class="d-flex flex-wrap gap-2"
              >
                <VChip
                  v-for="(tag, index) in moodBoardForm.tags"
                  :key="index"
                  closable
                  size="small"
                  @click:close="removeTag(index)"
                >
                  {{ tag }}
                </VChip>
              </div>
            </VCol>

            <!-- Settings -->
            <VCol cols="12">
              <VLabel class="mb-2">
                Settings
              </VLabel>
              <div class="d-flex flex-column gap-2">
                <VCheckbox
                  v-model="moodBoardForm.settings.showGrid"
                  label="Show grid"
                  density="compact"
                />
                <VCheckbox
                  v-model="moodBoardForm.settings.snapToGrid"
                  label="Snap to grid"
                  density="compact"
                />
                <VCheckbox
                  v-model="moodBoardForm.isPublic"
                  label="Make public"
                  density="compact"
                />
                <VCheckbox
                  v-model="moodBoardForm.isTemplate"
                  label="Save as template"
                  density="compact"
                />
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="outlined"
          @click="handleDialogClose"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          Create Mood Board
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
