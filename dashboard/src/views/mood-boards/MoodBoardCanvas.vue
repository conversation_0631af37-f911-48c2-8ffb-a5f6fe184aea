<script setup>
import { ref, computed, onUnmounted } from 'vue'

const props = defineProps({
  moodBoard: {
    type: Object,
    required: true,
  },
  selectedItem: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits([
  'itemSelect',
  'itemUpdate',
  'canvasClick',
])

// State
const canvasRef = ref(null)
const isDragging = ref(false)
const isResizing = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const dragItem = ref(null)
const resizeHandle = ref(null)
const resizeStartSize = ref({ width: 0, height: 0 })
const resizeStartPos = ref({ x: 0, y: 0 })

// Local state for smooth interactions
const localItemStates = ref(new Map())
const updateTimeout = ref(null)
const isUpdating = ref(false)

// Canvas style
const canvasStyle = computed(() => ({
  width: `${props.moodBoard.settings.canvasSize.width}px`,
  height: `${props.moodBoard.settings.canvasSize.height}px`,
  backgroundColor: props.moodBoard.settings.backgroundColor,
  backgroundImage: props.moodBoard.settings.showGrid ? 
    'radial-gradient(circle, #ccc 1px, transparent 1px)' : 'none',
  backgroundSize: props.moodBoard.settings.showGrid ? 
    `${props.moodBoard.settings.gridSize}px ${props.moodBoard.settings.gridSize}px` : 'auto',
}))

// Helper functions
function getLocalItemState(item) {
  const localState = localItemStates.value.get(item._id)
  return localState || item
}

function setLocalItemState(itemId, state) {
  localItemStates.value.set(itemId, state)
}

function clearLocalItemState(itemId) {
  localItemStates.value.delete(itemId)
}

function debouncedUpdate(item) {
  if (updateTimeout.value) {
    clearTimeout(updateTimeout.value)
  }

  isUpdating.value = true
  updateTimeout.value = setTimeout(() => {
    emit('itemUpdate', item)
    isUpdating.value = false
    clearLocalItemState(item._id)
  }, 100) // 100ms debounce for smooth dragging
}

// Methods
function handleCanvasClick(event) {
  if (event.target === canvasRef.value) {
    emit('canvasClick')
  }
}

function handleItemClick(item, event) {
  event.stopPropagation()
  emit('itemSelect', item)
}

function handleMouseDown(item, event) {
  // Check if this is a resize handle
  if (event.target.classList.contains('resize-handle')) {
    handleResizeStart(item, event)
    
    return
  }

  event.preventDefault()
  isDragging.value = true
  dragItem.value = item

  const rect = canvasRef.value.getBoundingClientRect()

  dragOffset.value = {
    x: event.clientX - rect.left - item.position.x,
    y: event.clientY - rect.top - item.position.y,
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

function handleMouseMove(event) {
  if (isResizing.value) {
    handleResizeMove(event)

    return
  }

  if (!isDragging.value || !dragItem.value) return

  const rect = canvasRef.value.getBoundingClientRect()
  let newX = event.clientX - rect.left - dragOffset.value.x
  let newY = event.clientY - rect.top - dragOffset.value.y

  // Snap to grid if enabled
  if (props.moodBoard.settings.snapToGrid) {
    const gridSize = props.moodBoard.settings.gridSize

    newX = Math.round(newX / gridSize) * gridSize
    newY = Math.round(newY / gridSize) * gridSize
  }

  // Keep within canvas bounds
  newX = Math.max(0, Math.min(newX, props.moodBoard.settings.canvasSize.width - dragItem.value.size.width))
  newY = Math.max(0, Math.min(newY, props.moodBoard.settings.canvasSize.height - dragItem.value.size.height))

  // Only update if position actually changed to prevent unnecessary updates
  if (dragItem.value.position.x !== newX || dragItem.value.position.y !== newY) {
    const updatedItem = {
      ...dragItem.value,
      position: { x: newX, y: newY },
    }

    // Update local state immediately for smooth UI
    setLocalItemState(dragItem.value._id, updatedItem)

    // Debounce the actual update to backend
    debouncedUpdate(updatedItem)
  }
}

function handleMouseUp() {
  // If we were dragging or resizing, send final update immediately
  if ((isDragging.value || isResizing.value) && dragItem.value) {
    if (updateTimeout.value) {
      clearTimeout(updateTimeout.value)
    }

    const localState = getLocalItemState(dragItem.value)
    if (localState !== dragItem.value) {
      emit('itemUpdate', localState)
      clearLocalItemState(dragItem.value._id)
    }
  }

  isDragging.value = false
  isResizing.value = false
  dragItem.value = null
  resizeHandle.value = null
  isUpdating.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

function getItemStyle(item) {
  // Use local state if available for smoother interactions
  const currentItem = getLocalItemState(item)

  let cursor = 'grab'
  if ((isDragging.value || isResizing.value) && dragItem.value?._id === item._id) {
    cursor = 'grabbing'
  }

  return {
    position: 'absolute',
    left: `${currentItem.position.x}px`,
    top: `${currentItem.position.y}px`,
    width: `${currentItem.size.width}px`,
    height: `${currentItem.size.height}px`,
    transform: `rotate(${currentItem.style.rotation || 0}deg)`,
    opacity: currentItem.style.opacity || 1,
    zIndex: currentItem.style.zIndex || 1,
    border: currentItem.style.borderWidth ?
      `${currentItem.style.borderWidth}px solid ${currentItem.style.borderColor || '#000'}` : 'none',
    borderRadius: `${currentItem.style.borderRadius || 0}px`,
    backgroundColor: currentItem.style.backgroundColor || 'transparent',
    cursor,
    transition: (isDragging.value || isResizing.value) && dragItem.value?._id === item._id ?
      'none' : 'all 0.1s ease-out',
  }
}

function getTextStyle(item) {
  return {
    fontSize: `${item.metadata.fontSize || 16}px`,
    fontFamily: item.metadata.fontFamily || 'Arial, sans-serif',
    fontWeight: item.metadata.fontWeight || 'normal',
    textAlign: item.metadata.textAlign || 'left',
    color: item.metadata.color || '#000000',
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: item.metadata.textAlign === 'center' ? 'center' : 
      item.metadata.textAlign === 'right' ? 'flex-end' : 'flex-start',
    padding: '8px',
    wordWrap: 'break-word',
    overflow: 'hidden',
  }
}

function isSelected(item) {
  return props.selectedItem && props.selectedItem._id === item._id
}

// Resize functionality
function handleResizeStart(item, event) {
  event.preventDefault()
  event.stopPropagation()

  isResizing.value = true
  dragItem.value = item
  resizeHandle.value = event.target.className.includes('resize-handle-') ?
    event.target.className.match(/resize-handle-(\w+)/)[1] : 'se'

  resizeStartSize.value = { ...item.size }
  resizeStartPos.value = { ...item.position }

  const rect = canvasRef.value.getBoundingClientRect()

  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

function handleResizeMove(event) {
  if (!isResizing.value || !dragItem.value || !resizeHandle.value) return

  const rect = canvasRef.value.getBoundingClientRect()
  const currentX = event.clientX - rect.left
  const currentY = event.clientY - rect.top

  const deltaX = currentX - dragOffset.value.x
  const deltaY = currentY - dragOffset.value.y

  let newWidth = resizeStartSize.value.width
  let newHeight = resizeStartSize.value.height
  let newX = resizeStartPos.value.x
  let newY = resizeStartPos.value.y

  // Handle different resize directions
  switch (resizeHandle.value) {
  case 'nw': // Northwest
    newWidth = resizeStartSize.value.width - deltaX
    newHeight = resizeStartSize.value.height - deltaY
    newX = resizeStartPos.value.x + deltaX
    newY = resizeStartPos.value.y + deltaY
    break
  case 'ne': // Northeast
    newWidth = resizeStartSize.value.width + deltaX
    newHeight = resizeStartSize.value.height - deltaY
    newY = resizeStartPos.value.y + deltaY
    break
  case 'sw': // Southwest
    newWidth = resizeStartSize.value.width - deltaX
    newHeight = resizeStartSize.value.height + deltaY
    newX = resizeStartPos.value.x + deltaX
    break
  case 'se': // Southeast (default)
  default:
    newWidth = resizeStartSize.value.width + deltaX
    newHeight = resizeStartSize.value.height + deltaY
    break
  }

  // Enforce minimum size
  const minSize = 20

  newWidth = Math.max(minSize, newWidth)
  newHeight = Math.max(minSize, newHeight)

  // Keep within canvas bounds
  newX = Math.max(0, Math.min(newX, props.moodBoard.settings.canvasSize.width - newWidth))
  newY = Math.max(0, Math.min(newY, props.moodBoard.settings.canvasSize.height - newHeight))

  // Adjust width/height if position was clamped
  if (newX === 0) {
    newWidth = Math.min(newWidth, props.moodBoard.settings.canvasSize.width)
  }
  if (newY === 0) {
    newHeight = Math.min(newHeight, props.moodBoard.settings.canvasSize.height)
  }

  // Snap to grid if enabled
  if (props.moodBoard.settings.snapToGrid) {
    const gridSize = props.moodBoard.settings.gridSize

    newWidth = Math.round(newWidth / gridSize) * gridSize
    newHeight = Math.round(newHeight / gridSize) * gridSize
    newX = Math.round(newX / gridSize) * gridSize
    newY = Math.round(newY / gridSize) * gridSize
  }

  // Only update if size or position actually changed
  if (dragItem.value.size.width !== newWidth ||
      dragItem.value.size.height !== newHeight ||
      dragItem.value.position.x !== newX ||
      dragItem.value.position.y !== newY) {

    const updatedItem = {
      ...dragItem.value,
      size: { width: newWidth, height: newHeight },
      position: { x: newX, y: newY },
    }

    // Update local state immediately for smooth UI
    setLocalItemState(dragItem.value._id, updatedItem)

    // Debounce the actual update to backend
    debouncedUpdate(updatedItem)
  }
}

// Cleanup
onUnmounted(() => {
  if (updateTimeout.value) {
    clearTimeout(updateTimeout.value)
  }
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  localItemStates.value.clear()
})
</script>

<template>
  <div class="mood-board-canvas-wrapper">
    <div
      ref="canvasRef"
      class="mood-board-canvas"
      :style="canvasStyle"
      @click="handleCanvasClick"
    >
      <!-- Render mood board items -->
      <div
        v-for="item in moodBoard.items"
        :key="item._id"
        class="mood-board-item"
        :class="{
          'selected': isSelected(item),
          'dragging': isDragging && dragItem?._id === item._id,
          'resizing': isResizing && dragItem?._id === item._id
        }"
        :style="getItemStyle(item)"
        @click="handleItemClick(item, $event)"
        @mousedown="handleMouseDown(item, $event)"
      >
        <!-- Image Item -->
        <img
          v-if="item.type === 'image'"
          :src="item.content"
          :alt="item.metadata.title || 'Mood board image'"
          class="mood-board-image"
          draggable="false"
        >
        
        <!-- Color Item -->
        <div
          v-else-if="item.type === 'color'"
          class="mood-board-color"
          :style="{ backgroundColor: item.content }"
        />
        
        <!-- Text Item -->
        <div
          v-else-if="item.type === 'text'"
          class="mood-board-text"
          :style="getTextStyle(item)"
        >
          {{ item.content }}
        </div>
        
        <!-- Link Item -->
        <div
          v-else-if="item.type === 'link'"
          class="mood-board-link"
        >
          <VIcon icon="tabler-link" />
          <span class="link-text">{{ item.metadata.title || item.content }}</span>
        </div>
        
        <!-- Selection Border -->
        <div
          v-if="isSelected(item)"
          class="selection-border"
        />
        
        <!-- Resize Handles -->
        <div
          v-if="isSelected(item)"
          class="resize-handles"
        >
          <div
            class="resize-handle resize-handle-nw"
            @mousedown="handleResizeStart(item, $event)"
          />
          <div
            class="resize-handle resize-handle-ne"
            @mousedown="handleResizeStart(item, $event)"
          />
          <div
            class="resize-handle resize-handle-sw"
            @mousedown="handleResizeStart(item, $event)"
          />
          <div
            class="resize-handle resize-handle-se"
            @mousedown="handleResizeStart(item, $event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mood-board-canvas-wrapper {
  padding: 20px;
  overflow: auto;
  min-height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  /* Improve scrolling performance */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.mood-board-canvas {
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  margin: 20px;
  /* Optimize for frequent repaints */
  transform: translateZ(0);
  backface-visibility: hidden;
}

.mood-board-item {
  user-select: none;
  will-change: transform, left, top, width, height;

  &:not(.dragging):not(.resizing) {
    transition: box-shadow 0.2s ease;
  }

  &:hover:not(.dragging):not(.resizing) {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  &.selected {
    box-shadow: 0 0 0 2px rgb(var(--v-theme-primary));
  }

  &.dragging {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transform: rotate(2deg) !important;
    z-index: 1000;
    transition: none;
  }

  &.resizing {
    box-shadow: 0 0 0 2px rgb(var(--v-theme-primary)), 0 8px 25px rgba(0, 0, 0, 0.2);
    transition: none;
  }
}

.mood-board-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

.mood-board-color {
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.mood-board-text {
  background: rgba(255, 255, 255, 0.9);
  border-radius: inherit;
}

.mood-board-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  
  .link-text {
    font-size: 14px;
    color: rgb(var(--v-theme-primary));
    text-decoration: underline;
  }
}

.selection-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid rgb(var(--v-theme-primary));
  border-radius: inherit;
  pointer-events: none;
}

.resize-handles {
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: rgb(var(--v-theme-primary));
  border: 2px solid white;
  border-radius: 50%;
  pointer-events: auto;
  z-index: 1000;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  &.resize-handle-nw {
    top: 0;
    left: 0;
    cursor: nw-resize;
  }

  &.resize-handle-ne {
    top: 0;
    right: 0;
    cursor: ne-resize;
  }

  &.resize-handle-sw {
    bottom: 0;
    left: 0;
    cursor: sw-resize;
  }

  &.resize-handle-se {
    bottom: 0;
    right: 0;
    cursor: se-resize;
  }
}
</style>
