<script setup>
import { ref, computed, watch, toRefs } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketTermConditionStore } from '@stores/terms-and-conditions/index'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  templates: {
    type: Array,
    default: () => [],
  },
  defaultType: {
    type: String,
    default: 'General',
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible, templates, defaultType } = toRefs(props)

const storeAuth = useSocketStore()
const termConditionStore = useSocketTermConditionStore()

const { user } = storeToRefs(storeAuth)

// Types
const types = [
  { value: 'General', title: 'General' },
  { value: 'Invoice', title: 'Invoice' },
  { value: 'Estimate', title: 'Estimate' },
  { value: 'Quote', title: 'Quote' },
  { value: 'Production', title: 'Production' },
  { value: 'AdvanceInvoice', title: 'Advance Invoice' },
  { value: 'License', title: 'License' },
  { value: 'Custom', title: 'Custom' },
]

// Document Types
const documentTypes = [
  { value: '', title: 'None' },
  { value: 'invoice', title: 'Invoice' },
  { value: 'estimate', title: 'Estimate' },
  { value: 'quote', title: 'Quote' },
  { value: 'production', title: 'Production' },
  { value: 'general', title: 'General' },
]

// Form data
const termConditionForm = ref({
  title: '',
  content: '',
  type: defaultType.value,
  template: '',
  variables: [],
  isActive: true,
  documentType: '',
  documentId: '',
  order: 0,
})

// Selected template
const selectedTemplate = ref(null)

// Form validation
const isFormValid = computed(() => {
  return termConditionForm.value.title.trim() !== '' && 
         termConditionForm.value.content.trim() !== ''
})

// Available templates for selected type
const availableTemplates = computed(() => {
  if (!templates.value) return []
  
  return templates.value.filter(template =>
    template.type === termConditionForm.value.type && template.isActive,
  )
})

// Watch for default type changes
watch(defaultType, newType => {
  if (newType && termConditionForm.value.type !== newType) {
    termConditionForm.value.type = newType
    termConditionForm.value.template = ''
    selectedTemplate.value = null
    termConditionForm.value.variables = []
  }
}, { immediate: true })

// Handle template selection
function handleTemplateChange() {
  if (termConditionForm.value.template) {
    selectedTemplate.value = templates.value.find(t => t._id === termConditionForm.value.template)
    if (selectedTemplate.value) {
      termConditionForm.value.content = selectedTemplate.value.content
      termConditionForm.value.variables = selectedTemplate.value.variables?.map(v => ({
        name: v.name,
        value: v.defaultValue || '',
      })) || []
    }
  } else {
    selectedTemplate.value = null
    termConditionForm.value.variables = []
  }
}

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  termConditionStore.createTermCondition({
    user: user.value._id,
    title: termConditionForm.value.title,
    content: termConditionForm.value.content,
    type: termConditionForm.value.type,
    template: termConditionForm.value.template || undefined,
    variables: termConditionForm.value.variables,
    isActive: termConditionForm.value.isActive,
    documentType: termConditionForm.value.documentType || undefined,
    documentId: termConditionForm.value.documentId || undefined,
    order: termConditionForm.value.order,
  })
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  termConditionForm.value = {
    title: '',
    content: '',
    type: defaultType.value,
    template: '',
    variables: [],
    isActive: true,
    documentType: '',
    documentId: '',
    order: 0,
  }
  selectedTemplate.value = null
}

// Watch dialog close to reset form
watch(isDialogVisible, newVal => {
  if (!newVal) {
    resetForm()
  }
})

// Watch type change to reset template
watch(() => termConditionForm.value.type, () => {
  termConditionForm.value.template = ''
  selectedTemplate.value = null
  termConditionForm.value.variables = []
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="900"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Create Terms & Conditions</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="termConditionForm.title"
                label="Title"
                placeholder="Enter a descriptive title"
                :rules="[v => !!v || 'Title is required']"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="termConditionForm.type"
                :items="types"
                item-title="title"
                item-value="value"
                label="Type"
                prepend-inner-icon="tabler-category"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="termConditionForm.template"
                :items="availableTemplates"
                item-title="templateName"
                item-value="_id"
                label="Template (Optional)"
                prepend-inner-icon="tabler-template"
                clearable
                @update:model-value="handleTemplateChange"
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="termConditionForm.content"
                label="Content"
                placeholder="Enter the terms and conditions content"
                rows="8"
                :rules="[v => !!v || 'Content is required']"
              />
            </VCol>

            <!-- Variables Section -->
            <VCol
              v-if="termConditionForm.variables.length > 0"
              cols="12"
            >
              <VLabel class="mb-2">
                Template Variables
              </VLabel>
              <VCard variant="outlined">
                <VCardText>
                  <VRow>
                    <VCol
                      v-for="(variable, index) in termConditionForm.variables"
                      :key="index"
                      cols="12"
                      md="6"
                    >
                      <AppTextField
                        v-model="variable.value"
                        :label="variable.name"
                        :placeholder="`Enter ${variable.name}`"
                        density="compact"
                      />
                    </VCol>
                  </VRow>
                </VCardText>
              </VCard>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="termConditionForm.documentType"
                :items="documentTypes"
                item-title="title"
                item-value="value"
                label="Document Type (Optional)"
                prepend-inner-icon="tabler-file"
                clearable
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model.number="termConditionForm.order"
                label="Order"
                type="number"
                placeholder="0"
                prepend-inner-icon="tabler-sort-ascending"
              />
            </VCol>

            <VCol cols="12">
              <VCheckbox
                v-model="termConditionForm.isActive"
                label="Active"
                density="compact"
              />
              <div class="text-caption text-medium-emphasis mt-1">
                Active terms and conditions will be available for use
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="outlined"
          @click="handleDialogClose"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          Create Terms & Conditions
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
