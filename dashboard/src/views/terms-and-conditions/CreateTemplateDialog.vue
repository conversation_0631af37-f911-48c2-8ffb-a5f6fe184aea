<script setup>
import { ref, computed, watch, toRefs } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketTermConditionTemplateStore } from '@stores/terms-and-conditions/templates'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const { isDialogVisible } = toRefs(props)

const storeAuth = useSocketStore()
const templateStore = useSocketTermConditionTemplateStore()

const { user } = storeToRefs(storeAuth)

// Types
const types = [
  { value: 'General', title: 'General' },
  { value: 'Invoice', title: 'Invoice' },
  { value: 'Estimate', title: 'Estimate' },
  { value: 'Quote', title: 'Quote' },
  { value: 'Production', title: 'Production' },
  { value: 'AdvanceInvoice', title: 'Advance Invoice' },
  { value: 'License', title: 'License' },
  { value: 'Custom', title: 'Custom' },
]

// Form data
const templateForm = ref({
  templateName: '',
  type: 'General',
  content: '',
  variables: [],
  tags: [],
  isActive: true,
  isDefault: false,
})

// Variable input
const variableForm = ref({
  name: '',
  description: '',
  defaultValue: '',
  type: 'text',
})

// Tag input
const tagInput = ref('')

// Form validation
const isFormValid = computed(() => {
  return templateForm.value.templateName.trim() !== '' && 
         templateForm.value.content.trim() !== ''
})

// Handle form submission
function handleSubmit() {
  if (!isFormValid.value) return

  templateStore.createTermConditionTemplate({
    user: user.value._id,
    templateName: templateForm.value.templateName,
    type: templateForm.value.type,
    content: templateForm.value.content,
    variables: templateForm.value.variables,
    tags: templateForm.value.tags,
    isActive: templateForm.value.isActive,
    isDefault: templateForm.value.isDefault,
  })
}

// Handle dialog close
function handleDialogClose() {
  emit('update:isDialogVisible', false)
  resetForm()
}

// Reset form
function resetForm() {
  templateForm.value = {
    templateName: '',
    type: 'General',
    content: '',
    variables: [],
    tags: [],
    isActive: true,
    isDefault: false,
  }
  variableForm.value = {
    name: '',
    description: '',
    defaultValue: '',
    type: 'text',
  }
  tagInput.value = ''
}

// Add variable
function addVariable() {
  if (variableForm.value.name.trim()) {
    templateForm.value.variables.push({ ...variableForm.value })
    variableForm.value = {
      name: '',
      description: '',
      defaultValue: '',
      type: 'text',
    }
  }
}

// Remove variable
function removeVariable(index) {
  templateForm.value.variables.splice(index, 1)
}

// Add tag
function addTag() {
  const tag = tagInput.value.trim()
  if (tag && !templateForm.value.tags.includes(tag)) {
    templateForm.value.tags.push(tag)
    tagInput.value = ''
  }
}

// Remove tag
function removeTag(index) {
  templateForm.value.tags.splice(index, 1)
}

// Handle tag input keypress
function handleTagKeypress(event) {
  if (event.key === 'Enter') {
    event.preventDefault()
    addTag()
  }
}

// Watch dialog close to reset form
watch(isDialogVisible, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="800"
    @update:model-value="handleDialogClose"
  >
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Create Terms & Conditions Template</span>
        <VBtn
          icon
          size="small"
          color="default"
          variant="text"
          @click="handleDialogClose"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </VCardTitle>

      <VDivider />

      <VCardText>
        <VForm @submit.prevent="handleSubmit">
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="templateForm.templateName"
                label="Template Name"
                placeholder="Enter a descriptive name"
                :rules="[v => !!v || 'Template name is required']"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="templateForm.type"
                :items="types"
                item-title="title"
                item-value="value"
                label="Type"
                prepend-inner-icon="tabler-category"
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="templateForm.content"
                label="Template Content"
                placeholder="Enter the terms and conditions content"
                rows="6"
                :rules="[v => !!v || 'Content is required']"
              />
            </VCol>

            <VCol cols="12">
              <VLabel class="mb-2">
                Variables
              </VLabel>
              <VCard
                variant="outlined"
                class="mb-3"
              >
                <VCardText>
                  <VRow>
                    <VCol
                      cols="12"
                      md="3"
                    >
                      <AppTextField
                        v-model="variableForm.name"
                        label="Variable Name"
                        placeholder="e.g., companyName"
                        density="compact"
                      />
                    </VCol>
                    <VCol
                      cols="12"
                      md="4"
                    >
                      <AppTextField
                        v-model="variableForm.description"
                        label="Description"
                        placeholder="e.g., Company Name"
                        density="compact"
                      />
                    </VCol>
                    <VCol
                      cols="12"
                      md="3"
                    >
                      <AppTextField
                        v-model="variableForm.defaultValue"
                        label="Default Value"
                        placeholder="Default value"
                        density="compact"
                      />
                    </VCol>
                    <VCol
                      cols="12"
                      md="2"
                    >
                      <VBtn
                        color="primary"
                        variant="outlined"
                        size="small"
                        @click="addVariable"
                      >
                        Add
                      </VBtn>
                    </VCol>
                  </VRow>
                </VCardText>
              </VCard>
              
              <div
                v-if="templateForm.variables.length > 0"
                class="mb-3"
              >
                <VChip
                  v-for="(variable, index) in templateForm.variables"
                  :key="index"
                  size="small"
                  closable
                  class="me-2 mb-2"
                  @click:close="removeVariable(index)"
                >
                  {{ variable.name }} ({{ variable.description }})
                </VChip>
              </div>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <div class="mb-3">
                <VLabel class="mb-2">
                  Tags
                </VLabel>
                <div class="d-flex gap-2 mb-2">
                  <AppTextField
                    v-model="tagInput"
                    placeholder="Add a tag"
                    density="compact"
                    @keypress="handleTagKeypress"
                  />
                  <VBtn
                    color="primary"
                    variant="outlined"
                    size="small"
                    @click="addTag"
                  >
                    Add
                  </VBtn>
                </div>
                <div class="d-flex flex-wrap gap-2">
                  <VChip
                    v-for="(tag, index) in templateForm.tags"
                    :key="index"
                    size="small"
                    closable
                    @click:close="removeTag(index)"
                  >
                    {{ tag }}
                  </VChip>
                </div>
              </div>
            </VCol>

            <VCol cols="12">
              <VRow>
                <VCol
                  cols="12"
                  md="6"
                >
                  <VCheckbox
                    v-model="templateForm.isActive"
                    label="Active template"
                    density="compact"
                  />
                </VCol>

                <VCol
                  cols="12"
                  md="6"
                >
                  <VCheckbox
                    v-model="templateForm.isDefault"
                    label="Set as default template"
                    density="compact"
                  />
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="outlined"
          @click="handleDialogClose"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          Create Template
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
