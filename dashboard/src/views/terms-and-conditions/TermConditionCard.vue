<script setup>
const props = defineProps({
  termCondition: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits([
  'edit',
  'view',
  'delete',
])

// Methods
function handleEdit() {
  emit('edit', props.termCondition)
}

function handleView() {
  emit('view', props.termCondition)
}

function handleDelete() {
  emit('delete', props.termCondition)
}

function formatDate(date) {
  return new Date(date).toLocaleDateString()
}

function getTypeColor(type) {
  const colors = {
    General: 'primary',
    Invoice: 'success',
    Estimate: 'info',
    Quote: 'warning',
    Production: 'purple',
    AdvanceInvoice: 'orange',
    License: 'pink',
    Custom: 'grey',
  }

  
  return colors[type] || 'grey'
}

function getTypeIcon(type) {
  const icons = {
    General: 'tabler-file-text',
    Invoice: 'tabler-receipt',
    Estimate: 'tabler-calculator',
    Quote: 'tabler-quote',
    Production: 'tabler-tools',
    AdvanceInvoice: 'tabler-receipt-2',
    License: 'tabler-license',
    Custom: 'tabler-settings',
  }

  
  return icons[type] || 'tabler-file-text'
}

function getDocumentTypeColor(documentType) {
  const colors = {
    invoice: 'success',
    estimate: 'info',
    quote: 'warning',
    production: 'purple',
    general: 'primary',
  }

  
  return colors[documentType] || 'default'
}

function getVariableCount(variables) {
  return Array.isArray(variables) ? variables.length : 0
}
</script>

<template>
  <VCard
    class="term-condition-card"
    hover
    @click="handleView"
  >
    <!-- Card Header -->
    <VCardTitle class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <VIcon
          :icon="getTypeIcon(termCondition.type)"
          class="me-2"
          :color="getTypeColor(termCondition.type)"
        />
        <span class="text-truncate">{{ termCondition.title }}</span>
      </div>
      
      <div class="d-flex gap-1">
        <VChip
          v-if="termCondition.order > 0"
          color="info"
          size="x-small"
          variant="tonal"
        >
          #{{ termCondition.order }}
        </VChip>
        <VChip
          v-if="!termCondition.isActive"
          color="error"
          size="x-small"
          variant="tonal"
        >
          Inactive
        </VChip>
      </div>
    </VCardTitle>

    <!-- Card Content -->
    <VCardText>
      <div class="d-flex align-center justify-space-between mb-3">
        <VChip
          :color="getTypeColor(termCondition.type)"
          size="small"
          variant="tonal"
        >
          {{ termCondition.type }}
        </VChip>
        
        <div
          v-if="termCondition.documentType"
          class="d-flex align-center gap-2"
        >
          <VChip
            :color="getDocumentTypeColor(termCondition.documentType)"
            size="small"
            variant="outlined"
          >
            {{ termCondition.documentType }}
          </VChip>
        </div>
      </div>

      <p class="text-body-2 text-medium-emphasis mb-3 text-truncate-3">
        {{ termCondition.content || 'No content provided' }}
      </p>

      <!-- Variables and Template Info -->
      <div class="d-flex align-center justify-space-between mb-3">
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-variable"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ getVariableCount(termCondition.variables) }} variables
          </span>
        </div>
        
        <div
          v-if="termCondition.template"
          class="d-flex align-center gap-2"
        >
          <VIcon
            icon="tabler-template"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            From template
          </span>
        </div>
      </div>

      <!-- Date and Identity -->
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center gap-2">
          <VIcon
            icon="tabler-calendar"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ formatDate(termCondition.createdAt) }}
          </span>
        </div>
        
        <div
          v-if="termCondition.identity"
          class="d-flex align-center gap-2"
        >
          <VIcon
            icon="tabler-user"
            size="16"
            class="text-medium-emphasis"
          />
          <span class="text-caption text-medium-emphasis">
            {{ termCondition.identity.name }}
          </span>
        </div>
      </div>

      <!-- Version info -->
      <div
        v-if="termCondition.version > 1"
        class="d-flex align-center gap-2 mt-2"
      >
        <VIcon
          icon="tabler-versions"
          size="16"
          class="text-medium-emphasis"
        />
        <span class="text-caption text-medium-emphasis">
          Version {{ termCondition.version }}
        </span>
      </div>
    </VCardText>

    <!-- Card Actions -->
    <VCardActions>
      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-eye"
        @click.stop="handleView"
      >
        View
      </VBtn>
      
      <VBtn
        variant="text"
        size="small"
        prepend-icon="tabler-edit"
        @click.stop="handleEdit"
      >
        Edit
      </VBtn>
      
      <VSpacer />
      
      <VBtn
        variant="text"
        size="small"
        color="error"
        icon
        @click.stop="handleDelete"
      >
        <VIcon icon="tabler-trash" />
      </VBtn>
    </VCardActions>
  </VCard>
</template>

<style lang="scss" scoped>
.term-condition-card {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
