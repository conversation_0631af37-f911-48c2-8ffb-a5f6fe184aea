<script setup>
import { ref } from 'vue'
import Doughn<PERSON><PERSON><PERSON> from '@/@core/libs/chartjs/components/Doughnut<PERSON><PERSON>'
import Bar<PERSON>hart from '@/@core/libs/chartjs/components/BarChart'

// Beautiful Color Palette
const colorPalette = {
  purple: '#C5A2FF',
  blue: '#4CF3EA',
  yellow: '#FED56A',
  green: '#76FFCE',
  red: '#E57B7B',
  pink: '#F51BC5',
}

const colors = Object.values(colorPalette)

// ApexCharts Donut Chart
const donutChartSeries = ref([44, 55, 13, 43, 22, 35])

const donutChartOptions = ref({
  chart: {
    type: 'donut',
    fontFamily: 'Inter, sans-serif',
  },
  colors: colors,
  labels: ['Purple', 'Blue', 'Yellow', 'Green', 'Red', 'Pink'],
  legend: {
    position: 'bottom',
    fontSize: '14px',
  },
  plotOptions: {
    pie: {
      donut: {
        size: '70%',
        labels: {
          show: true,
          name: {
            fontSize: '16px',
            fontWeight: 600,
          },
          value: {
            fontSize: '20px',
            fontWeight: 700,
          },
          total: {
            show: true,
            fontSize: '16px',
            fontWeight: 600,
          },
        },
      },
    },
  },
  dataLabels: {
    enabled: false,
  },
})

// ApexCharts Line Chart
const lineChartSeries = ref([
  {
    name: 'Revenue',
    data: [30, 40, 35, 50, 49, 60, 70, 91, 125],
  },
  {
    name: 'Profit',
    data: [20, 30, 25, 40, 39, 50, 60, 81, 105],
  },
])

const lineChartOptions = ref({
  chart: {
    type: 'line',
    fontFamily: 'Inter, sans-serif',
    toolbar: {
      show: false,
    },
  },
  colors: [colorPalette.pink, colorPalette.purple],
  stroke: {
    curve: 'smooth',
    width: 3,
  },
  markers: {
    size: 6,
    strokeWidth: 2,
    strokeColors: '#fff',
  },
  xaxis: {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'],
  },
  legend: {
    position: 'top',
  },
  grid: {
    borderColor: '#f1f1f1',
  },
})

// ApexCharts Bar Chart
const barChartSeries = ref([
  {
    name: 'Sales',
    data: [44, 55, 57, 56, 61, 58, 63, 60, 66],
  },
  {
    name: 'Orders',
    data: [76, 85, 101, 98, 87, 105, 91, 114, 94],
  },
])

const barChartOptions = ref({
  chart: {
    type: 'bar',
    fontFamily: 'Inter, sans-serif',
    toolbar: {
      show: false,
    },
  },
  colors: [colorPalette.blue, colorPalette.green],
  plotOptions: {
    bar: {
      horizontal: false,
      columnWidth: '55%',
      borderRadius: 4,
    },
  },
  dataLabels: {
    enabled: false,
  },
  xaxis: {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'],
  },
  legend: {
    position: 'top',
  },
})

// ApexCharts Area Chart
const areaChartSeries = ref([
  {
    name: 'Users',
    data: [31, 40, 28, 51, 42, 109, 100],
  },
  {
    name: 'Sessions',
    data: [11, 32, 45, 32, 34, 52, 41],
  },
])

const areaChartOptions = ref({
  chart: {
    type: 'area',
    fontFamily: 'Inter, sans-serif',
    toolbar: {
      show: false,
    },
  },
  colors: [colorPalette.yellow, colorPalette.red],
  fill: {
    type: 'gradient',
    gradient: {
      shadeIntensity: 1,
      opacityFrom: 0.7,
      opacityTo: 0.3,
    },
  },
  stroke: {
    curve: 'smooth',
    width: 2,
  },
  xaxis: {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
  },
  legend: {
    position: 'top',
  },
})

// Chart.js Data
const chartjsDonutData = ref({
  labels: ['Purple', 'Blue', 'Yellow', 'Green', 'Red', 'Pink'],
  datasets: [
    {
      data: [300, 50, 100, 80, 120, 200],
      backgroundColor: colors,
      borderWidth: 0,
      hoverOffset: 4,
    },
  ],
})

const chartjsBarData = ref({
  labels: ['January', 'February', 'March', 'April', 'May', 'June'],
  datasets: [
    {
      label: 'Revenue',
      data: [65, 59, 80, 81, 56, 55],
      backgroundColor: colors.map(color => `${color}80`),
      borderColor: colors,
      borderWidth: 2,
      borderRadius: 4,
    },
  ],
})

const chartjsOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
    },
  },
})
</script>

<template>
  <VRow>
    <!-- Color Palette Display -->
    <VCol cols="12">
      <VCard>
        <VCardTitle class="text-h5 mb-4">
          🎨 Beautiful Chart Color Palette
        </VCardTitle>
        <VCardText>
          <VRow>
            <VCol
              v-for="(color, name) in colorPalette"
              :key="name"
              cols="2"
              class="text-center"
            >
              <div
                class="color-swatch mx-auto mb-2"
                :style="{ backgroundColor: color }"
              />
              <div class="text-caption font-weight-medium">
                {{ name.charAt(0).toUpperCase() + name.slice(1) }}
              </div>
              <div class="text-caption text-medium-emphasis">
                {{ color }}
              </div>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>

    <!-- Beautiful Donut Chart -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardTitle>Beautiful Donut Chart</VCardTitle>
        <VCardText>
          <VueApexCharts
            :options="donutChartOptions"
            :series="donutChartSeries"
            type="donut"
            height="350"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- Beautiful Line Chart -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardTitle>Beautiful Line Chart</VCardTitle>
        <VCardText>
          <VueApexCharts
            :options="lineChartOptions"
            :series="lineChartSeries"
            type="line"
            height="350"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- Beautiful Bar Chart -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardTitle>Beautiful Bar Chart</VCardTitle>
        <VCardText>
          <VueApexCharts
            :options="barChartOptions"
            :series="barChartSeries"
            type="bar"
            height="350"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- Beautiful Area Chart -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardTitle>Beautiful Area Chart</VCardTitle>
        <VCardText>
          <VueApexCharts
            :options="areaChartOptions"
            :series="areaChartSeries"
            type="area"
            height="350"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- Chart.js Examples -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardTitle>Chart.js Doughnut</VCardTitle>
        <VCardText>
          <DoughnutChart
            :chart-data="chartjsDonutData"
            :chart-options="chartjsOptions"
            :height="300"
          />
        </VCardText>
      </VCard>
    </VCol>

    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardTitle>Chart.js Bar Chart</VCardTitle>
        <VCardText>
          <BarChart
            :chart-data="chartjsBarData"
            :chart-options="chartjsOptions"
            :height="300"
          />
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style scoped>
.color-swatch {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid white;
}
</style>
