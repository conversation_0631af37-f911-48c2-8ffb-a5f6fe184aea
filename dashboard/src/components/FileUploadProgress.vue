<script setup>
const props = defineProps({
  progress: {
    type: Number,
  },
})
</script>

<template>
  <div class="loader-wrapper">
    <div class="body-loader">
      <span>
        <span />
        <span />
        <span />
        <span />
      </span>
      <div class="base">
        <span />
        <div class="face" />
      </div>
    </div>

    <div class="longfazers">
      <span />
      <span />
      <span />
      <span />
    </div>

    <h1>{{ props.progress }}</h1>
  </div>
</template>

<style scoped lang="scss">
.loader-wrapper {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: rgb(var(0,0,0,0.5));
  overflow: hidden;
}

h1 {
  position: absolute;
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  font-size: 32px;
  text-transform: uppercase;
  color: #F51BC5;
  left: 50%;
  top: 55%;
  transform: translateX(-50%);
}

.body-loader {
  position: absolute;
  top: 50%;
  left: 46%;
  transform: translate(-50%, -50%);
  animation: speeder 0.4s linear infinite;

  > span {
    height: 5px;
    width: 35px;
    background: #F51BC5;
    position: absolute;
    top: -19px;
    left: 60px;
    border-radius: 2px 10px 1px 0;

    > span {
      width: 30px;
      height: 1px;
      background: #F51BC5;
      position: absolute;
    }

    > span:nth-child(1) {
      top: 0;
      animation: fazer1 0.2s linear infinite;
    }

    > span:nth-child(2) {
      top: 3px;
      animation: fazer2 0.4s linear infinite;
    }

    > span:nth-child(3) {
      top: 1px;
      animation: fazer3 0.4s linear infinite;
      animation-delay: -1s;
    }

    > span:nth-child(4) {
      top: 4px;
      animation: fazer4 1s linear infinite;
      animation-delay: -1s;
    }
  }
}

.base span {
  position: absolute;
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-right: 100px solid #F51BC5;
  border-bottom: 6px solid transparent;

  &::before {
    content: '';
    height: 22px;
    width: 22px;
    border-radius: 50%;
    background: #F51BC5;
    position: absolute;
    right: -110px;
    top: -16px;
  }

  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-top: 0 solid transparent;
    border-right: 55px solid #F51BC5;
    border-bottom: 16px solid transparent;
    top: -16px;
    right: -98px;
  }
}

.face {
  position: absolute;
  height: 12px;
  width: 20px;
  background: #F51BC5;
  border-radius: 20px 20px 0 0;
  transform: rotate(-40deg);
  right: -125px;
  top: -15px;

  &::after {
    content: '';
    height: 12px;
    width: 12px;
    background: #F51BC5;
    position: absolute;
    right: 4px;
    top: 7px;
    transform: rotate(40deg);
    transform-origin: 50% 50%;
    border-radius: 0 0 0 2px;
  }
}

/* FAZER animations */
@keyframes fazer1 {
  0% { left: 0; }
  100% { left: -80px; opacity: 0; }
}
@keyframes fazer2 {
  0% { left: 0; }
  100% { left: -100px; opacity: 0; }
}
@keyframes fazer3 {
  0% { left: 0; }
  100% { left: -50px; opacity: 0; }
}
@keyframes fazer4 {
  0% { left: 0; }
  100% { left: -150px; opacity: 0; }
}

/* Speeder wobble */
@keyframes speeder {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  10% { transform: translate(-51%, -53%) rotate(-1deg); }
  20% { transform: translate(-52%, -50%) rotate(1deg); }
  30% { transform: translate(-49%, -48%) rotate(0deg); }
  40% { transform: translate(-49%, -51%) rotate(1deg); }
  50% { transform: translate(-51%, -47%) rotate(-1deg); }
  60% { transform: translate(-51%, -49%) rotate(0deg); }
  70% { transform: translate(-47%, -49%) rotate(-1deg); }
  80% { transform: translate(-52%, -51%) rotate(1deg); }
  90% { transform: translate(-48%, -50%) rotate(0deg); }
  100% { transform: translate(-49%, -52%) rotate(-1deg); }
}

/* Longfazers */
.longfazers {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 5;

  span {
    position: absolute;
    height: 2px;
    width: 200px;
    background: #F51BC5;
    left: 0;

    &:nth-child(1) {
      top: 20%;
      animation: lf 1.5s linear infinite;
    }
    &:nth-child(2) {
      top: 40%;
      animation: lf2 1.8s linear infinite;
    }
    &:nth-child(3) {
      top: 60%;
      animation: lf3 1.3s linear infinite;
    }
    &:nth-child(4) {
      top: 80%;
      animation: lf4 1.6s linear infinite;
    }
  }
}

@keyframes lf {
  0% { left: 120%; opacity: 1; }
  100% { left: -200px; opacity: 0; }
}
@keyframes lf2 {
  0% { left: 100%; opacity: 1; }
  100% { left: -250px; opacity: 0; }
}
@keyframes lf3 {
  0% { left: 130%; opacity: 1; }
  100% { left: -300px; opacity: 0; }
}
@keyframes lf4 {
  0% { left: 110%; opacity: 1; }
  100% { left: -200px; opacity: 0; }
}
</style>
