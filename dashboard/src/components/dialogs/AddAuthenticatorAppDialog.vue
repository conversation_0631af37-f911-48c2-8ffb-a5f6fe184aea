<script setup>
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import themeselectionQr from '@images/pages/themeselection-qr.png'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'

const props = defineProps({
  authCode: {
    type: String,
    required: false,
  },
  image: {
    type: String,
    required: false,
  },
  setupCode: {
    type: String,
    required: false,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'submit',
])

const store = useSocketStore()

const { user, token } = storeToRefs(store)

const vuetifyTheme = useTheme()

socket.on('createVerify2fa', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    user.value = data.data.user

    emit('update:isDialogVisible', false)

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

const authCode = ref(structuredClone(toRaw(props.authCode)))

const verifyCode = ref('')

const formSubmit = () => {
  if (verifyCode.value) {
    store.verify2Fa({
      id: user.value._id,
      token: verifyCode.value,
    })

    /*emit('submit', authCode.value)
    emit('update:isDialogVisible', false)*/
  }
}

const resetAuthCode = () => {
  authCode.value = structuredClone(toRaw(props.authCode))
  emit('update:isDialogVisible', false)
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="props.isDialogVisible"
    @update:model-value="(val) => $emit('update:isDialogVisible', val)"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-2 pa-sm-10">
      <VCardText>
        <!-- 👉 Title -->
        <h4 class="text-h4 text-center mb-6">
          Add Authenticator App
        </h4>
        <h5 class="text-h5 mb-2">
          Authenticator Apps
        </h5>

        <p class="text-body-1 mb-6">
          Using an authenticator app like Google Authenticator, Microsoft Authenticator, Authy, or 1Password, scan the QR code. It will generate a 6 digit code for you to enter below.
        </p>

        <div class="mb-6">
          <VImg
            width="250"
            :src="props.image"
            class="mx-auto"
          />
        </div>

        <VAlert
          :title="props.setupCode"
          text="If you are unable to scan the QR code, you can manually enter the secret key below."
          variant="tonal"
          color="warning"
        />
        <VForm @submit.prevent="formSubmit">
          <h6 class="text-body-1">
            Type your 6 digit security code
          </h6>
          <VOtpInput
            v-model="verifyCode"
            type="number"
            autofocus
            class="pa-0"
            @finish="formSubmit"
          />

          <div class="d-flex justify-end flex-wrap gap-4">
            <VBtn
              color="secondary"
              variant="tonal"
              @click="resetAuthCode"
            >
              Cancel
            </VBtn>

            <VBtn
              type="submit"
              @click="formSubmit"
            >
              Continue
              <VIcon
                end
                icon="tabler-arrow-right"
                class="flip-in-rtl"
              />
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
