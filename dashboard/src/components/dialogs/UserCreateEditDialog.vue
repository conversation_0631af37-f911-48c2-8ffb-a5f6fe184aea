<script setup>
import { requiredValidator, emailValidator } from '@/@core/utils/validators'

const props = defineProps({
  userData: {
    type: Object,
    required: false,
    default: () => ({
      _id: null,
      username: '',
      email: '',
      role: 'user',
      status: 'active',
      accountActive: true,
      isOtpActive: false,
      isMfaActive: false,
      isAffiliate: false,
      newUser: true,
      verifyEmailToken: false,
      password: '',
    }),
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'submit',
  'update:isDialogVisible',
])

const userData = ref(JSON.parse(JSON.stringify(props.userData)))
const isPasswordVisible = ref(false)

const roleOptions = [
  { title: 'User', value: 'user' },
  { title: 'Admin', value: 'admin' },
]

const statusOptions = [
  { title: 'Active', value: 'active' },
  { title: 'Inactive', value: 'inactive' },
  { title: 'Pending', value: 'pending' },
  { title: 'Suspended', value: 'suspended' },
]

watch(() => props.userData, newUserData => {
  userData.value = JSON.parse(JSON.stringify(newUserData))
}, { deep: true })

const onFormSubmit = () => {
  emit('submit', userData.value)
  emit('update:isDialogVisible', false)
}

const onFormReset = () => {
  userData.value = JSON.parse(JSON.stringify(props.userData))
  emit('update:isDialogVisible', false)
}

const dialogModelValueUpdate = val => {
  emit('update:isDialogVisible', val)
}

const isEditMode = computed(() => !!userData.value._id)
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="props.isDialogVisible"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard class="pa-sm-10 pa-2">
      <VCardText>
        <!-- 👉 Title -->
        <h4 class="text-h4 text-center mb-2">
          {{ isEditMode ? 'Edit User' : 'Create New User' }}
        </h4>
        <p class="text-body-1 text-center mb-6">
          {{ isEditMode ? 'Update user information and settings.' : 'Add a new user to the system.' }}
        </p>

        <!-- 👉 Form -->
        <VForm
          class="mt-6"
          @submit.prevent="onFormSubmit"
        >
          <VRow>
            <!-- 👉 Username -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userData.username"
                label="Username"
                placeholder="john.doe"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Email -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userData.email"
                label="Email"
                placeholder="<EMAIL>"
                type="email"
                :rules="[requiredValidator, emailValidator]"
              />
            </VCol>

            <!-- 👉 Password (only for create) -->
            <VCol
              v-if="!isEditMode"
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userData.password"
                label="Password"
                placeholder="Enter password"
                :type="isPasswordVisible ? 'text' : 'password'"
                :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                :rules="[requiredValidator]"
                @click:append-inner="isPasswordVisible = !isPasswordVisible"
              />
            </VCol>

            <!-- 👉 Role -->
            <VCol
              cols="12"
              md="6"
            >
              <AppSelect
                v-model="userData.role"
                label="Role"
                placeholder="Select Role"
                :items="roleOptions"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Status -->
            <VCol
              cols="12"
              md="6"
            >
              <AppSelect
                v-model="userData.status"
                label="Status"
                placeholder="Select Status"
                :items="statusOptions"
              />
            </VCol>

            <!-- 👉 Account Active -->
            <VCol
              cols="12"
              md="6"
            >
              <VSwitch
                v-model="userData.accountActive"
                label="Account Active"
                color="primary"
              />
            </VCol>

            <!-- 👉 OTP Active -->
            <VCol
              cols="12"
              md="6"
            >
              <VSwitch
                v-model="userData.isOtpActive"
                label="OTP Active"
                color="primary"
              />
            </VCol>

            <!-- 👉 MFA Active -->
            <VCol
              cols="12"
              md="6"
            >
              <VSwitch
                v-model="userData.isMfaActive"
                label="MFA Active"
                color="primary"
              />
            </VCol>

            <!-- 👉 Is Affiliate -->
            <VCol
              cols="12"
              md="6"
            >
              <VSwitch
                v-model="userData.isAffiliate"
                label="Is Affiliate"
                color="primary"
              />
            </VCol>

            <!-- 👉 New User -->
            <VCol
              cols="12"
              md="6"
            >
              <VSwitch
                v-model="userData.newUser"
                label="New User"
                color="primary"
              />
            </VCol>

            <!-- 👉 Email Verified -->
            <VCol
              cols="12"
              md="6"
            >
              <VSwitch
                v-model="userData.verifyEmailToken"
                label="Email Verified"
                color="primary"
              />
            </VCol>

            <!-- 👉 Submit and Cancel -->
            <VCol
              cols="12"
              class="d-flex flex-wrap justify-center gap-4"
            >
              <VBtn
                type="submit"
                color="primary"
              >
                {{ isEditMode ? 'Update' : 'Create' }}
              </VBtn>

              <VBtn
                color="secondary"
                variant="outlined"
                @click="onFormReset"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
