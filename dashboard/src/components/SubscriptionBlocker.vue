<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useSocketStore } from '@stores/auth'
import { useSubscriptionSocketStore } from '@stores/subscription'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import { useTheme } from 'vuetify'

const props = defineProps({
  isBlocked: {
    type: Boolean,
    required: true,
  },
  blockReason: {
    type: String,
    default: '',
  },
  subscriptionDetails: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['upgrade', 'logout'])

const store = useSocketStore()

const { user } = storeToRefs(store)

const router = useRouter()
const userStore = useSocketStore()
const subscriptionStore = useSubscriptionSocketStore()
const vuetifyTheme = useTheme()

// Reactive state
const selectedPlan = ref(null)
const billingCycle = ref('monthly')
const availablePlans = ref([])
const isUpgrading = ref(false)

// Fallback plans in case socket doesn't work
const fallbackPlans = [
  {
    _id: 'basic',
    title: 'Basic',
    priceMonthly: 280,
    priceYearly: 2480,
    isPopular: false,
    paystackPlanCodes: {
      monthly: 'PLN_lt6ibafwu0fa26l',
      yearly: 'PLN_mmxnqegms4umb1w',
    },
  },
  {
    _id: 'allin',
    title: 'All In',
    priceMonthly: 540,
    priceYearly: 4800,
    isPopular: true,
    paystackPlanCodes: {
      monthly: 'PLN_79dz0av80r8a56v',
      yearly: 'PLN_ea7er3kx4tvfbz2',
    },
  },
  {
    _id: 'production',
    title: 'Production',
    priceMonthly: 1100,
    priceYearly: 9600,
    isPopular: false,
    paystackPlanCodes: {
      monthly: 'PLN_x01uu0iabedar51',
      yearly: 'PLN_kdvlus5vynenupv',
    },
  },
  {
    _id: 'nolimits',
    title: 'No Limits',
    priceMonthly: 2700,
    priceYearly: 24000,
    isPopular: false,
    paystackPlanCodes: {
      monthly: 'PLN_5u5gl75qhetxa4u',
      yearly: 'PLN_4rp5wgoa8h79n4q',
    },
  },
]

const isTrialExpired = computed(() => {
  if (!props.subscriptionDetails?.subscription) return false

  const subscription = props.subscriptionDetails.subscription
  
  return subscription.status === 'expired' || subscription.status === 'trial'
})

const formatDate = dateString => {
  if (!dateString) return 'N/A'
  
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

// Plan selection
const selectPlan = plan => {
  selectedPlan.value = plan
  console.log('Selected plan:', plan)

  // Provide user feedback
  toast(`${plan.title} plan selected`, {
    autoClose: 2000,
    theme: vuetifyTheme.global.name.value,
    type: 'success',
  })
}

// Load subscription packages
const loadPlans = () => {
  console.log('Loading subscription plans...')

  // Use fallback plans immediately for testing
  availablePlans.value = fallbackPlans

  const popularPlan = availablePlans.value.find(plan => plan.isPopular)
  if (popularPlan) {
    selectedPlan.value = popularPlan
    console.log('Auto-selected popular plan (fallback):', popularPlan.title)
  }

  // Also try to load from socket
  subscriptionStore.getFrontendSubscriptionPackages()

  // Set a timeout to use fallback if socket doesn't respond
  setTimeout(() => {
    if (availablePlans.value.length === 0) {
      console.log('Socket timeout, using fallback plans')
      availablePlans.value = fallbackPlans

      const popularPlan = availablePlans.value.find(plan => plan.isPopular)
      if (popularPlan) {
        selectedPlan.value = popularPlan
      }
    }
  }, 3000)
}

// Handle subscription packages response
const handleSubscriptionPackages = data => {
  console.log('Received subscription packages:', data)

  if (data.status === 'success') {
    availablePlans.value = data.data || []
    console.log('Loaded plans:', availablePlans.value)

    // Auto-select the most popular plan
    const popularPlan = availablePlans.value.find(plan => plan.isPopular)
    if (popularPlan) {
      selectedPlan.value = popularPlan
      console.log('Auto-selected popular plan:', popularPlan.title)
    } else if (availablePlans.value.length > 0) {
      selectedPlan.value = availablePlans.value[0]
      console.log('Auto-selected first plan:', availablePlans.value[0].title)
    }
  } else {
    console.error('Failed to load subscription packages:', data.message)
    toast('Failed to load subscription plans. Please refresh the page.', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
}

// Upgrade now - initiate payment
const upgradeNow = () => {
  if (!selectedPlan.value) {
    toast('Please select a plan first', {
      autoClose: 3000,
      theme: vuetifyTheme.global.name.value,
      type: 'warning',
    })
    
    return
  }

  if (!userStore.user?._id) {
    toast('User not found. Please login again.', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    
    return
  }

  isUpgrading.value = true

  const plan = selectedPlan.value
  const planType = `${plan.title.toLowerCase().replace(' ', '')}${billingCycle.value === 'yearly' ? 'Yearly' : 'Monthly'}`
  const price = billingCycle.value === 'yearly' ? plan.priceYearly : plan.priceMonthly

  // Get the Paystack plan code
  const paystackPlanCode = billingCycle.value === 'yearly'
    ? plan.paystackPlanCodes?.yearly
    : plan.paystackPlanCodes?.monthly

  if (!paystackPlanCode) {
    toast('Plan configuration error. Please contact support.', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    isUpgrading.value = false
    
    return
  }

  const upgradeData = {
    userId: userStore.user._id,
    email: userStore.user.email,
    paystackPlan: paystackPlanCode,
    planType: planType,
    price: price,
    planId: plan._id,
  }

  console.log('Initiating upgrade:', {
    plan: plan.title,
    billingCycle: billingCycle.value,
    price,
    paystackPlanCode,
    upgradeData,
  })

  // Use upgradeFromTrial to initiate payment
  subscriptionStore.upgradeFromTrial(upgradeData)
}

const viewPlans = () => {
  router.push('/subscription')
}

const logout = () => {
  emit('logout')
  store.logout({
    id: userStore.user._id,
    user: userStore.user._id,
  })
  router.push('/auth/login')
}

// Socket listeners
const setupSocketListeners = () => {
  socket.on('getFrontendSubscriptionPackages', handleSubscriptionPackages)

  // Listen for Paystack redirect
  socket.on('redirectToPaystack', data => {
    console.log('Redirecting to Paystack:', data)
    isUpgrading.value = false

    if (data.status === 'success') {
      toast('Redirecting to payment...', {
        autoClose: 3000,
        theme: vuetifyTheme.global.name.value,
        type: 'info',
      })

      // Redirect to Paystack - handle both data structures
      const redirectUrl = data.data?.url || data.url
      if (redirectUrl) {
        window.location.href = redirectUrl
      } else {
        console.error('No redirect URL found in response:', data)
        toast('Payment redirect failed. Please try again.', {
          autoClose: 5000,
          theme: vuetifyTheme.global.name.value,
          type: 'error',
        })
      }
    } else {
      console.error('Payment redirect failed:', data)
      toast(data.message || 'Payment redirect failed. Please try again.', {
        autoClose: 5000,
        theme: vuetifyTheme.global.name.value,
        type: 'error',
      })
    }
  })

  // Listen for subscription errors
  socket.on('subscriptionError', data => {
    isUpgrading.value = false

    toast(data.message || 'An error occurred during subscription upgrade', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  })
}

const cleanupSocketListeners = () => {
  socket.off('getFrontendSubscriptionPackages', handleSubscriptionPackages)
  socket.off('redirectToPaystack')
  socket.off('subscriptionError')
}

// Load plans when component mounts
onMounted(() => {
  setupSocketListeners()
  loadPlans()
})

onUnmounted(() => {
  cleanupSocketListeners()
})
</script>

<template>
  <VDialog
    :model-value="isBlocked"
    persistent
    no-click-animation
    :width="$vuetify.display.smAndDown ? 'auto' : 600"
    class="subscription-blocker"
  >
    <VCard class="text-center pa-8">
      <VCardText>
        <!-- Icon -->
        <div class="mb-6">
          <VIcon
            icon="tabler-lock"
            size="80"
            color="warning"
          />
        </div>

        <!-- Title -->
        <h2 class="text-h4 mb-4">
          {{ isTrialExpired ? 'Trial Expired' : 'Subscription Required' }}
        </h2>

        <!-- Message -->
        <p class="text-body-1 mb-6">
          {{ blockReason || 'Your trial has expired. Please upgrade to continue using QwoteZ.' }}
        </p>

        <!-- Subscription Details -->
        <VCard
          v-if="subscriptionDetails"
          variant="tonal"
          color="warning"
          class="mb-6"
        >
          <VCardText>
            <div class="d-flex flex-column gap-2">
              <div class="d-flex justify-space-between">
                <span class="font-weight-medium">Status:</span>
                <VChip
                  :color="subscriptionDetails.subscription?.status === 'expired' ? 'error' : 'warning'"
                  size="small"
                  variant="flat"
                >
                  {{ subscriptionDetails.subscription?.status || 'Unknown' }}
                </VChip>
              </div>
              
              <div 
                v-if="subscriptionDetails.subscription?.trialEndsAt"
                class="d-flex justify-space-between"
              >
                <span class="font-weight-medium">Trial Ended:</span>
                <span>{{ formatDate(subscriptionDetails.subscription.trialEndsAt) }}</span>
              </div>
              
              <div class="d-flex justify-space-between">
                <span class="font-weight-medium">Current Plan:</span>
                <span>{{ subscriptionDetails.currentPlan?.title || 'None' }}</span>
              </div>
            </div>
          </VCardText>
        </VCard>

        <!-- Features Lost -->
        <VAlert
          type="info"
          variant="tonal"
          class="mb-6 text-start"
        >
          <template #title>
            What you're missing:
          </template>
          <ul class="mt-2">
            <li>Create and manage quotes</li>
            <li>Access to premium templates</li>
            <li>Advanced analytics</li>
            <li>Customer management</li>
            <li>Export and sharing features</li>
          </ul>
        </VAlert>

        <!-- Plan Selection -->
        <div class="mb-6">
          <h3 class="text-h6 mb-4">
            Choose Your Plan:
          </h3>

          <!-- Debug info -->
          <div
            v-if="availablePlans.length === 0"
            class="text-center mb-4"
          >
            <VProgressCircular
              indeterminate
              color="primary"
              size="24"
            />
            <p class="text-caption mt-2">
              Loading plans...
            </p>
          </div>

          <VRow v-if="availablePlans.length > 0">
            <VCol
              v-for="plan in availablePlans"
              :key="plan._id"
              cols="12"
              sm="6"
              md="3"
            >
              <VCard
                :class="{
                  'border-primary border-opacity-100': selectedPlan?._id === plan._id,
                  'elevation-8': selectedPlan?._id === plan._id,
                  'bg-primary-lighten-5': selectedPlan?._id === plan._id
                }"
                :variant="selectedPlan?._id === plan._id ? 'elevated' : 'outlined'"
                :color="selectedPlan?._id === plan._id ? 'primary' : undefined"
                class="cursor-pointer transition-all position-relative"
                @click="selectPlan(plan)"
              >
                <!-- Selection indicator -->
                <VIcon
                  v-if="selectedPlan?._id === plan._id"
                  icon="tabler-check-circle"
                  color="primary"
                  size="24"
                  class="position-absolute"
                  style="top: 8px; right: 8px; z-index: 1;"
                />

                <VCardText class="text-center pa-4">
                  <h4 class="text-h6 mb-2">
                    {{ plan.title }}
                  </h4>
                  <div class="text-h5 text-primary mb-2">
                    R{{ billingCycle === 'yearly' ? plan.priceYearly : plan.priceMonthly }}
                  </div>
                  <div class="text-caption text-medium-emphasis">
                    per {{ billingCycle === 'yearly' ? 'year' : 'month' }}
                  </div>
                  <div class="mt-2">
                    <VChip
                      v-if="plan.isPopular"
                      color="primary"
                      size="small"
                      variant="flat"
                    >
                      Popular
                    </VChip>
                  </div>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </div>

        <!-- Billing Cycle Toggle -->
        <div class="mb-6">
          <VBtnToggle
            v-model="billingCycle"
            color="primary"
            variant="outlined"
            divided
            mandatory
          >
            <VBtn value="monthly">
              Monthly
            </VBtn>
            <VBtn value="yearly">
              Yearly
              <VChip
                color="success"
                size="x-small"
                class="ml-2"
              >
                Save 20%
              </VChip>
            </VBtn>
          </VBtnToggle>
        </div>

        <!-- Action Buttons -->
        <div class="d-flex flex-column gap-4">
          <VBtn
            color="primary"
            size="large"
            variant="elevated"
            :disabled="!selectedPlan || isUpgrading"
            :loading="isUpgrading"
            @click="upgradeNow"
          >
            <VIcon
              start
              icon="tabler-crown"
            />
            {{ isUpgrading ? 'Processing...' : 'Upgrade Now' }}
          </VBtn>

          <VBtn
            variant="outlined"
            @click="viewPlans"
          >
            View All Plans & Features
          </VBtn>

          <VBtn
            variant="text"
            size="small"
            @click="logout"
          >
            Logout
          </VBtn>
        </div>

        <!-- Contact Support -->
        <div class="mt-6 pt-4 border-t">
          <p class="text-caption text-medium-emphasis">
            Need help? 
            <a 
              href="mailto:<EMAIL>"
              class="text-primary text-decoration-none"
            >
              Contact Support
            </a>
          </p>
        </div>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped>
.subscription-blocker {
  z-index: 9999;
}

.subscription-blocker :deep(.v-overlay__content) {
  max-height: 90vh;
  overflow-y: auto;
}

.cursor-pointer {
  cursor: pointer;
}

.transition-all {
  transition: all 0.3s ease;
}

.cursor-pointer:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced selection styling */
.cursor-pointer.border-primary {
  border-width: 2px !important;
  box-shadow: 0 0 0 1px rgb(var(--v-theme-primary)) !important;
}

.bg-primary-lighten-5 {
  background-color: rgba(var(--v-theme-primary), 0.05) !important;
}
</style>
