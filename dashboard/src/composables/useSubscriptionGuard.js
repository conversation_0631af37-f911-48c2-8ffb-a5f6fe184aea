import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useSocketStore } from '@stores/auth'
import { useSubscriptionSocketStore } from '@stores/subscription'
import { toast } from 'vue3-toastify'
import { useTheme } from 'vuetify'
import { socket } from '@socket/socket'

export function useSubscriptionGuard() {
  const router = useRouter()
  const userStore = useSocketStore()
  const subscriptionStore = useSubscriptionSocketStore()
  const vuetifyTheme = useTheme()

  const isBlocked = ref(false)
  const blockReason = ref('')
  const subscriptionDetails = ref(null)

  // Computed properties for subscription status
  const isTrialExpired = computed(() => {
    if (!subscriptionDetails.value?.subscription) return false

    const subscription = subscriptionDetails.value.subscription
    const now = new Date()

    console.log('Checking subscription status:', {
      status: subscription.status,
      trialEndsAt: subscription.trialEndsAt,
      endsAt: subscription.endsAt,
      now: now.toISOString()
    })

    // Check if trial has expired
    if (subscription.status === 'trial' && subscription.trialEndsAt) {
      const trialExpired = new Date(subscription.trialEndsAt) <= now
      console.log('Trial expired check:', trialExpired)
      return trialExpired
    }

    // Check if subscription has expired
    if (subscription.status === 'expired') {
      console.log('Subscription marked as expired')
      return true
    }

    // Check if paid subscription has expired
    if (subscription.status === 'active' && subscription.endsAt) {
      const subscriptionExpired = new Date(subscription.endsAt) <= now
      console.log('Paid subscription expired check:', subscriptionExpired)
      return subscriptionExpired
    }

    return false
  })

  const isSubscriptionActive = computed(() => {
    if (!subscriptionDetails.value?.subscription) return false
    
    const subscription = subscriptionDetails.value.subscription
    return subscription.status === 'active'
  })

  const daysRemaining = computed(() => {
    if (!subscriptionDetails.value?.subscription) return 0
    
    const subscription = subscriptionDetails.value.subscription
    return subscription.daysRemaining || 0
  })

  const shouldBlockAccess = computed(() => {
    // Block if trial is expired or subscription is expired
    return isTrialExpired.value && !isSubscriptionActive.value
  })

  // Check subscription status
  const checkSubscriptionStatus = async () => {
    try {
      const user = userStore.user
      if (!user?._id) {
        console.log('No user found, redirecting to login')
        router.push('/auth/login')
        return
      }

      // Get subscription details
      subscriptionStore.getUserSubscriptionDetails({
        id: user._id,
        user: user._id
      })
    } catch (error) {
      console.error('Error checking subscription status:', error)
    }
  }

  // Handle subscription details response
  const handleSubscriptionResponse = (data) => {
    if (data.status === 'success') {
      subscriptionDetails.value = data.data
      
      if (shouldBlockAccess.value) {
        blockUser('Your trial has expired. Please upgrade to continue using QwoteZ.')
      } else {
        unblockUser()
      }
    } else {
      console.error('Failed to get subscription details:', data.message)
    }
  }

  // Block user access
  const blockUser = (reason) => {
    isBlocked.value = true
    blockReason.value = reason
    
    toast(reason, {
      autoClose: 8000,
      theme: vuetifyTheme.global.name.value,
      type: 'warning',
    })
    
    // Redirect to subscription page
    if (router.currentRoute.value.name !== 'subscription') {
      router.push('/subscription')
    }
  }

  // Unblock user access
  const unblockUser = () => {
    isBlocked.value = false
    blockReason.value = ''
  }

  // Force upgrade - redirect to subscription page
  const forceUpgrade = () => {
    router.push('/subscription')
  }

  // Watch for subscription changes
  watch(shouldBlockAccess, (newValue) => {
    console.log('shouldBlockAccess changed:', newValue)
    if (newValue) {
      const message = isTrialExpired.value
        ? 'Your trial has expired. Please upgrade to continue using QwoteZ.'
        : 'Your subscription has expired. Please upgrade to continue.'
      blockUser(message)
    } else {
      unblockUser()
    }
  })

  // Periodic check for subscription status (every 5 minutes)
  let intervalId = null
  const startPeriodicCheck = () => {
    intervalId = setInterval(() => {
      if (userStore.user?._id) {
        console.log('Periodic subscription check...')
        checkSubscriptionStatus()
      }
    }, 5 * 60 * 1000) // 5 minutes
  }

  const stopPeriodicCheck = () => {
    if (intervalId) {
      clearInterval(intervalId)
      intervalId = null
    }
  }

  return {
    isBlocked,
    blockReason,
    subscriptionDetails,
    isTrialExpired,
    isSubscriptionActive,
    daysRemaining,
    shouldBlockAccess,
    checkSubscriptionStatus,
    handleSubscriptionResponse,
    blockUser,
    unblockUser,
    forceUpgrade,
    startPeriodicCheck,
    stopPeriodicCheck
  }
}
