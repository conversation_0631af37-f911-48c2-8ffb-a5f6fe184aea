// 👉 Redirects
export const redirects = [
  // ℹ️ We are redirecting to different pages based on role.
  // NOTE: Role is just for UI purposes. ACL is based on abilities.
  {
    path: '/',
    name: 'index',
    redirect: to => {
      const userData = JSON.parse(sessionStorage.getItem('userData')) || {}
      const userRole = (userData && userData.role) ? userData.role : null

      console.log(userRole)

      if (userRole === 'admin')
        return { name: 'admin-dashboard' }
      if (userRole === 'user')
        return { name: 'root' }

      return { name: 'auth-login', query: to.query }
    },
  },
]
export const routes = []
