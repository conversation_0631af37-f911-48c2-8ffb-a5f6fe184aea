import { createMongoAbility } from '@casl/ability'
import { abilitiesPlugin } from '@casl/vue'

export default function (app) {
  const userAbilityRules = JSON.parse(localStorage.getItem('userAbilities')) || [{
    action: 'read',
    subject: 'standard',
  }]

  const initialAbility = createMongoAbility(userAbilityRules ?? [])

  initialAbility.update(userAbilityRules)

  console.log('initialAbility', userAbilityRules)

  app.use(abilitiesPlugin, initialAbility, {
    useGlobalProperties: true,
  })
}
