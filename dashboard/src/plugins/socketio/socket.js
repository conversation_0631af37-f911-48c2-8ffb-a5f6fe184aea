import { reactive } from 'vue'
import { io } from 'socket.io-client'

export const state = reactive({
  connected: false,
})

const token = sessionStorage.getItem('accessToken')
const user = JSON.parse(sessionStorage.getItem('userData'))

let userId = ''

if(user) {
  userId = user._id
}

const URL = import.meta.env.VITE_API_BASE_URL

console.log({ URL })

export const socket = io(URL, {
  withCredentials: true,
  auth: { token: token, userId: userId },
  reconnection: true,
  forceNew: true,
  transportOptions: {
    polling: {
      extraHeaders: {
        // 'Access-Control-Allow-Origin': ['https://api.qwotez.com', 'https://app.qwotez', 'https://qwotez.com', 'http://localhost:5173'],
      },
    },
  },
})

const tryReconnect = () => {
  setTimeout(() => {
    socket.io.open(err => {
      if (err) {
        tryReconnect()
      }
    })
  }, 2000)
}

socket.io.on('close', tryReconnect)
