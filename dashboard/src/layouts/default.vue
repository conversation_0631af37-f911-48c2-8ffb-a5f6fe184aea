<script setup>
import { useConfigStore } from '@core/stores/config'
import { AppContentLayoutNav } from '@layouts/enums'
import { switchToVerticalNavOnLtOverlayNavBreakpoint } from '@layouts/utils'
import { useSubscriptionGuard } from '@/composables/useSubscriptionGuard'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import SubscriptionBlocker from '@/components/SubscriptionBlocker.vue'

const DefaultLayoutWithHorizontalNav = defineAsyncComponent(() => import('./components/DefaultLayoutWithHorizontalNav.vue'))
const DefaultLayoutWithVerticalNav = defineAsyncComponent(() => import('./components/DefaultLayoutWithVerticalNav.vue'))
const configStore = useConfigStore()
const userStore = useSocketStore()

const { user } = storeToRefs(userStore)

// Subscription guard setup
const {
  isBlocked,
  blockReason,
  subscriptionDetails,
  checkSubscriptionStatus,
  handleSubscriptionResponse,
  startPeriodicCheck,
  stopPeriodicCheck,
} = useSubscriptionGuard()

// ℹ️ This will switch to vertical nav when define breakpoint is reached when in horizontal nav layout

// Remove below composable usage if you are not using horizontal nav layout in your app
switchToVerticalNavOnLtOverlayNavBreakpoint()

const { layoutAttrs, injectSkinClasses } = useSkins()

injectSkinClasses()

// SECTION: Loading Indicator
const isFallbackStateActive = ref(false)
const refLoadingIndicator = ref(null)

watch([
  isFallbackStateActive,
  refLoadingIndicator,
], () => {
  if (isFallbackStateActive.value && refLoadingIndicator.value)
    refLoadingIndicator.value.fallbackHandle()
  if (!isFallbackStateActive.value && refLoadingIndicator.value)
    refLoadingIndicator.value.resolveHandle()
}, { immediate: true })
// !SECTION

// Check subscription status when layout mounts
onMounted(() => {
  if (userStore.user?._id) {
    checkSubscriptionStatus()
    startPeriodicCheck()
  }
})

// Listen for subscription details response
socket.on('getUserSubscriptionDetails', handleSubscriptionResponse)

// Clean up socket listeners and periodic check
onUnmounted(() => {
  socket.off('getUserSubscriptionDetails', handleSubscriptionResponse)
  stopPeriodicCheck()
})
</script>

<template>
  <Component
    v-bind="layoutAttrs"
    :is="configStore.appContentLayoutNav === AppContentLayoutNav.Vertical ? DefaultLayoutWithVerticalNav : DefaultLayoutWithHorizontalNav"
  >
    <AppLoadingIndicator ref="refLoadingIndicator" />

    <RouterView v-slot="{ Component }">
      <Suspense
        :timeout="0"
        @fallback="isFallbackStateActive = true"
        @resolve="isFallbackStateActive = false"
      >
        <Component :is="Component" />
      </Suspense>
    </RouterView>

    <!-- Subscription Blocker -->
    <SubscriptionBlocker
      :is-blocked="isBlocked"
      :block-reason="blockReason"
      :subscription-details="subscriptionDetails"
      @upgrade="checkSubscriptionStatus"
      @logout="userStore.logout({ id: user._id, user: user._id })"
    />
  </Component>
</template>

<style lang="scss">
// As we are using `layouts` plugin we need its styles to be imported
@use "@layouts/styles/default-layout";
</style>
