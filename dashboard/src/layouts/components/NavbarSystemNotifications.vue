<script setup>
import { useSocketStore } from '@stores/auth'
import { useNotificationsSocketStore } from '@stores/notifications'
import { socket } from '@socket/socket'
import avatar3 from '@images/avatars/avatar-3.png'
import avatar4 from '@images/avatars/avatar-4.png'
import avatar5 from '@images/avatars/avatar-5.png'
import paypal from '@images/cards/paypal-rounded.png'
import SystemNotifications from '@core/components/SystemNotifications.vue'

const store = useSocketStore()
const storeNotification = useNotificationsSocketStore()

const { user } = storeToRefs(store)
const { appNotifications } = storeToRefs(storeNotification)

socket.on('markAsReadSystem', data => {
  appNotifications.value = data.data
})

socket.on('markAsUnreadSystem', data => {
  appNotifications.value = data.data
})

const removeNotification = notificationId => {
  appNotifications.value.forEach((item, index) => {
    if (notificationId === item.id)
      appNotifications.value.splice(index, 1)
  })
}

const markRead = notificationId => {
  appNotifications.value.forEach(item => {
    notificationId.forEach(id => {
      if (id === item.id)
        item.isRead = true
    })
  })

  storeNotification.markAsReadNotification({
    user: user.value._id,
    id: notificationId[0],
  })
}

const markUnRead = notificationId => {
  appNotifications.value.forEach(item => {
    notificationId.forEach(id => {
      if (id === item.id)
        item.isRead = false
    })
  })

  storeNotification.markAsUnreadNotification({
    user: user.value._id,
    id: notificationId[0],
  })
}

const handleNotificationClick = notification => {
  if (!notification.isRead)
    markRead([notification.id])
}
</script>

<template>
  <SystemNotifications
    v-if="appNotifications"
    :notifications="appNotifications"
    @remove="removeNotification"
    @read="markRead"
    @unread="markUnRead"
    @click:notification="handleNotificationClick"
  />
</template>
