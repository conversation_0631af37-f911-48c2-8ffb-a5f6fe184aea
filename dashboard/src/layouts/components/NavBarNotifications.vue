<script setup>
import { useSocketStore } from '@stores/auth'
import { useNotificationsSocketStore } from '@stores/notifications'
import { socket } from '@socket/socket'
import avatar3 from '@images/avatars/avatar-3.png'
import avatar4 from '@images/avatars/avatar-4.png'
import avatar5 from '@images/avatars/avatar-5.png'
import paypal from '@images/cards/paypal-rounded.png'

const store = useSocketStore()
const storeNotification = useNotificationsSocketStore()

const { user } = storeToRefs(store)
const { notifications } = storeToRefs(storeNotification)

socket.on('markAsRead', data => {
  notifications.value = data.data
})

socket.on('markAsUnread', data => {
  notifications.value = data.data
})

const removeNotification = notificationId => {
  notifications.value.forEach((item, index) => {
    if (notificationId === item.id)
      notifications.value.splice(index, 1)
  })
}

const markRead = notificationId => {
  notifications.value.forEach(item => {
    notificationId.forEach(id => {
      if (id === item.id)
        item.isRead = true
    })
  })

  storeNotification.markNotificationAsRead({
    user: user.value._id,
    id: notificationId[0],
  })
}

const markUnRead = notificationId => {
  notifications.value.forEach(item => {
    notificationId.forEach(id => {
      if (id === item.id)
        item.isRead = false
    })
  })

  storeNotification.markNotificationAsUnread({
    user: user.value._id,
    id: notificationId[0],
  })
}

const handleNotificationClick = notification => {
  if (!notification.isRead)
    markRead([notification.id])
}
</script>

<template>
  <Notifications
    v-if="notifications"
    :notifications="notifications"
    @remove="removeNotification"
    @read="markRead"
    @unread="markUnRead"
    @click:notification="handleNotificationClick"
  />
</template>
