<script setup>
import { useSocketStore } from '@stores/auth'
import avatar1 from '@images/avatars/avatar-1.png'
import { useTheme } from 'vuetify'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { socket } from '@socket/socket'

const store = useSocketStore()

const { user } = storeToRefs(store)

const router = useRouter()
const vuetifyTheme = useTheme()

socket.on('logout', data => {
  console.log({ data })

  switch (data.status) {
  case 'success':
    toast('Logged Out Successfully!', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    localStorage.clear()
    sessionStorage.clear()

    setTimeout(() => {
      router.go({ name: 'auth-login' })
    }, 2000)
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

const baseUrl = 'https://api.qwotez.com'

const logout = () => {
  console.log('logout')
  store.logout({
    id: user.value._id,
    currentSessionId: user.value.currentSessionId,
  })

  // useCookie('qwote-z-theme').value = 'light'
}
</script>

<template>
  <VBadge
    dot
    location="bottom right"
    offset-x="3"
    offset-y="3"
    bordered
    color="success"
  >
    <VAvatar
      class="cursor-pointer"
      color="primary"
      variant="tonal"
    >
      <VImg
        v-if="!user.profile"
        :src="avatar1"
      />
      <VImg
        v-else
        :src="baseUrl + user.profile.profileImgUrl + '/' + user.profile.profileImg"
      />

      <!-- SECTION Menu -->
      <VMenu
        activator="parent"
        width="230"
        location="bottom end"
        offset="14px"
      >
        <VList>
          <!-- 👉 User Avatar & Name -->
          <VListItem>
            <template #prepend>
              <VListItemAction start>
                <VBadge
                  dot
                  location="bottom right"
                  offset-x="3"
                  offset-y="3"
                  color="success"
                >
                  <VAvatar
                    v-if="!user.profile"
                    color="primary"
                    variant="tonal"
                  >
                    <VImg :src="avatar1" />
                  </VAvatar>
                  <VAvatar
                    v-else
                    color="primary"
                    variant="tonal"
                  >
                    <VImg :src="baseUrl + user.profile.profileImgUrl + '/' + user.profile.profileImg" />
                  </VAvatar>
                </VBadge>
              </VListItemAction>
            </template>

            <VListItemTitle
              v-if="user.profile"
              class="font-weight-semibold"
            >
              {{ user.profile.fullName }}
            </VListItemTitle>
            <VListItemSubtitle>{{ user.role }}</VListItemSubtitle>
          </VListItem>

          <VDivider class="my-2" />

          <!-- 👉 Profile -->
          <VListItem
            link
            :to="{ name: 'user-profile' }"
          >
            <template #prepend>
              <VIcon
                class="me-2"
                icon="tabler-user"
                size="22"
              />
            </template>

            <VListItemTitle>Profile</VListItemTitle>
          </VListItem>

          <!-- 👉 Settings -->
          <VListItem
            link
            :to="{ name: 'user-settings' }"
          >
            <template #prepend>
              <VIcon
                class="me-2"
                icon="tabler-settings"
                size="22"
              />
            </template>

            <VListItemTitle>Settings</VListItemTitle>
          </VListItem>

          <!-- 👉 FAQ -->
          <VListItem link>
            <template #prepend>
              <VIcon
                class="me-2"
                icon="tabler-help"
                size="22"
              />
            </template>

            <VListItemTitle>FAQ</VListItemTitle>
          </VListItem>

          <!-- Divider -->
          <VDivider class="my-2" />

          <!-- 👉 Logout -->
          <VListItem @click="logout">
            <template #prepend>
              <VIcon
                class="me-2"
                icon="tabler-logout"
                size="22"
              />
            </template>

            <VListItemTitle>Logout</VListItemTitle>
          </VListItem>
        </VList>
      </VMenu>
      <!-- !SECTION -->
    </VAvatar>
  </VBadge>
</template>
