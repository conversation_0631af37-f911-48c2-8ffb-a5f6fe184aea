import { useAbility } from '@casl/vue'

/**
 * Returns ability result if <PERSON><PERSON> is configured or else just return true
 * We should allow passing string | undefined to can because for admin ability we omit defining action & subject
 *
 * Useful if you don't know if <PERSON><PERSON> is configured or not
 * Used in @core files to handle absence of <PERSON>L without errors
 *
 * @param {string} action CASL Actions // https://casl.js.org/v4/en/guide/intro#basics
 * @param {string} subject CASL Subject // https://casl.js.org/v4/en/guide/intro#basics
 */
export const can = (action, subject) => {
  const vm = getCurrentInstance()
  if (!vm) return false

  const localCan = vm.proxy && '$can' in vm.proxy
  const abilityCan = localCan ? vm.proxy.$can : () => true

  const actions = Array.isArray(action) ? action : [action]
  const subjects = Array.isArray(subject) ? subject : [subject]

  return actions.some(act =>
    subjects.some(sub =>
      abilityCan(act, sub),
    ),
  )
}

/**
 * Check if user can view item based on it's ability
 * Based on item's action and subject & Hide group if all of it's children are hidden
 * @param {object} item navigation object item
 */
export const canViewNavMenuGroup = item => {
  const hasAnyVisibleChild = item.children?.some(i => can(i.action || i.actions, i.subject || i.subjects))

  if (!(item.action || item.actions) || !(item.subject || item.subjects))
    return hasAnyVisibleChild

  return can(item.action || item.actions, item.subject || item.subjects) && hasAnyVisibleChild
}
export const canNavigate = to => {
  const ability = useAbility()
  const targetRoute = to.matched[to.matched.length - 1]

  const actions = targetRoute?.meta?.actions || [targetRoute?.meta?.action]
  const subjects = targetRoute?.meta?.subjects || [targetRoute?.meta?.subject]

  return actions.some(action =>
    subjects.some(subject =>
      ability.can(action, subject),
    ),
  )
}
