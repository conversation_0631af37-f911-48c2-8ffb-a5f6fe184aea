@use "placeholders";
@use "@configured-variables" as variables;

@mixin rtl {
  @if variables.$enable-rtl-styles {
    [dir="rtl"] & {
      @content;
    }
  }
}

@mixin boxed-content($nest-selector: false) {
  & {
    @extend %boxed-content-spacing;

    @at-root {
      @if $nest-selector == false {
        .layout-content-width-boxed#{&} {
          @extend %boxed-content;
        }
      }
      // stylelint-disable-next-line @stylistic/indentation
      @else {
        .layout-content-width-boxed & {
          @extend %boxed-content;
        }
      }
    }
  }
}
