{"include": ["./vite.config.*", "./src/**/*", "./src/**/*.vue", "./themeConfig.js"], "exclude": ["./dist", "./node_modules"], "compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "preserve", "paths": {"@/*": ["./src/*"], "@themeConfig": ["./themeConfig.js"], "@layouts/*": ["./src/@layouts/*"], "@layouts": ["./src/@layouts"], "@core/*": ["./src/@core/*"], "@core": ["./src/@core"], "@images/*": ["./src/assets/images/*"], "@stores/*": ["./src/stores/*"], "@socket/*": ["./src/plugins/socketio/*"], "@utils/*": ["./src/utils/*"], "@styles/*": ["./src/assets/styles/*"], "@validators": ["./src/@core/utils/validators"], "@db/*": ["./src/plugins/fake-api/handlers/*"], "@api-utils/*": ["./src/plugins/fake-api/utils/*"]}, "types": ["vite/client", "unplugin-vue-router/client", "vite-plugin-vue-layouts/client"]}}